<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_dashboardview" modulePackage="Manaknight" filePath="app\src\main\res\layout-sw600dp\fragment_dashboardview.xml" directory="layout-sw600dp" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout-sw600dp/fragment_dashboardview_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="489" endOffset="51"/></Target><Target tag="binding_1" view="LinearLayout"><Expressions/><location startLine="7" startOffset="4" endLine="487" endOffset="18"/></Target><Target id="@+id/headerInclude" tag="binding_1" include="header"><Expressions/><location startLine="14" startOffset="8" endLine="16" endOffset="37"/></Target><Target id="@+id/statusFilterButton" view="TextView"><Expressions/><location startLine="49" startOffset="28" endLine="66" endOffset="93"/></Target><Target id="@+id/monthFilterButton" view="TextView"><Expressions/><location startLine="77" startOffset="28" endLine="94" endOffset="93"/></Target><Target id="@+id/txtDashboardTotalProfitOverhead" view="TextView"><Expressions/><location startLine="146" startOffset="40" endLine="157" endOffset="70"/></Target><Target id="@+id/txtDashboardCompanyHealth" view="TextView"><Expressions/><location startLine="200" startOffset="40" endLine="211" endOffset="70"/></Target><Target id="@+id/txtDashboardTotalContracts" view="TextView"><Expressions/><location startLine="242" startOffset="28" endLine="255" endOffset="67"/></Target><Target id="@+id/txtDashboardTotalAR" view="TextView"><Expressions/><location startLine="277" startOffset="28" endLine="291" endOffset="67"/></Target><Target id="@+id/txtDashboardMaterialBalance" view="TextView"><Expressions/><location startLine="314" startOffset="28" endLine="328" endOffset="67"/></Target><Target id="@+id/txtDashboardMaterialSpent" view="TextView"><Expressions/><location startLine="350" startOffset="28" endLine="364" endOffset="67"/></Target><Target id="@+id/txtDashboardLaborBalance" view="TextView"><Expressions/><location startLine="387" startOffset="28" endLine="401" endOffset="67"/></Target><Target id="@+id/txtDashboardLaborSpent" view="TextView"><Expressions/><location startLine="423" startOffset="28" endLine="437" endOffset="67"/></Target><Target id="@+id/recyclerViewProjects" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="472" startOffset="24" endLine="477" endOffset="60"/></Target></Targets></Layout>