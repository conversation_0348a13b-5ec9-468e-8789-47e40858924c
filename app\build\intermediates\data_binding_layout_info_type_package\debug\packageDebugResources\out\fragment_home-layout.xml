<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_home" modulePackage="Manaknight" filePath="app\src\main\res\layout\fragment_home.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_home_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="657" endOffset="51"/></Target><Target tag="binding_1" view="LinearLayout"><Expressions/><location startLine="6" startOffset="4" endLine="655" endOffset="18"/></Target><Target id="@+id/headerInclude" tag="binding_1" include="header"><Expressions/><location startLine="13" startOffset="8" endLine="15" endOffset="37"/></Target><Target id="@+id/txtTotalProfitOverhead" view="TextView"><Expressions/><location startLine="76" startOffset="36" endLine="90" endOffset="75"/></Target><Target id="@+id/txtCompanyHealth" view="TextView"><Expressions/><location startLine="136" startOffset="36" endLine="150" endOffset="75"/></Target><Target id="@+id/btnViewDetails" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="163" startOffset="16" endLine="177" endOffset="49"/></Target><Target id="@+id/txtTotalContracts" view="TextView"><Expressions/><location startLine="206" startOffset="28" endLine="220" endOffset="67"/></Target><Target id="@+id/txtTotalAR" view="TextView"><Expressions/><location startLine="242" startOffset="28" endLine="257" endOffset="67"/></Target><Target id="@+id/txtMaterialBalance" view="TextView"><Expressions/><location startLine="289" startOffset="28" endLine="304" endOffset="67"/></Target><Target id="@+id/txtMaterialSpent" view="TextView"><Expressions/><location startLine="326" startOffset="28" endLine="341" endOffset="67"/></Target><Target id="@+id/txtLaborBalance" view="TextView"><Expressions/><location startLine="372" startOffset="28" endLine="387" endOffset="67"/></Target><Target id="@+id/txtLaborSpent" view="TextView"><Expressions/><location startLine="409" startOffset="28" endLine="424" endOffset="67"/></Target><Target id="@+id/firstRow" view="LinearLayout"><Expressions/><location startLine="450" startOffset="20" endLine="532" endOffset="34"/></Target><Target id="@+id/draftsCard" view="LinearLayout"><Expressions/><location startLine="457" startOffset="24" endLine="492" endOffset="38"/></Target><Target id="@+id/txtDraftsCount" view="TextView"><Expressions/><location startLine="466" startOffset="28" endLine="480" endOffset="67"/></Target><Target id="@+id/outstandingCard" view="LinearLayout"><Expressions/><location startLine="496" startOffset="24" endLine="531" endOffset="38"/></Target><Target id="@+id/txtOutstandingCount" view="TextView"><Expressions/><location startLine="505" startOffset="28" endLine="519" endOffset="67"/></Target><Target id="@+id/activeCard" view="LinearLayout"><Expressions/><location startLine="545" startOffset="24" endLine="580" endOffset="38"/></Target><Target id="@+id/txtActiveCount" view="TextView"><Expressions/><location startLine="554" startOffset="28" endLine="568" endOffset="67"/></Target><Target id="@+id/completedCard" view="LinearLayout"><Expressions/><location startLine="584" startOffset="24" endLine="619" endOffset="38"/></Target><Target id="@+id/txtCompletedCount" view="TextView"><Expressions/><location startLine="593" startOffset="28" endLine="607" endOffset="67"/></Target><Target id="@+id/addNewEstimation" view="ImageView"><Expressions/><location startLine="623" startOffset="20" endLine="631" endOffset="62"/></Target></Targets></Layout>