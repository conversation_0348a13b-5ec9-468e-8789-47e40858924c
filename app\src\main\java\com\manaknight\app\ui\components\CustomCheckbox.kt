package com.manaknight.app.ui.components

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.unit.dp
import Manaknight.R
import androidx.compose.foundation.layout.padding

/**
 * Custom checkbox with brand green background and black checkmark
 */
@Composable
fun CustomCheckbox(
    checked: <PERSON><PERSON><PERSON>,
    onCheckedChange: ((<PERSON>olean) -> Unit)?,
    modifier: Modifier = Modifier,
    enabled: Boolean = true
) {
    val brandGreen = colorResource(R.color.brand_green)
    val uncheckedColor = Color.Gray
    val checkmarkColor = Color.Black

    Box(
        modifier = modifier
            .padding(horizontal = 8.dp) // Always apply left margin
            .then(modifier) // Allow user to still pass additional modifiers
            .size(24.dp)
            .clip(RoundedCornerShape(4.dp))

            .background(
                color = if (checked) brandGreen else Color.Transparent
            )
            .border(
                width = 2.dp,
                color = if (checked) brandGreen else uncheckedColor,
                shape = RoundedCornerShape(4.dp)
            )
            .padding(horizontal = 2.dp)
            .clickable(enabled = enabled) {
                onCheckedChange?.invoke(!checked)
            },
        contentAlignment = Alignment.Center
    ) {
        if (checked) {
            Canvas(
                modifier = Modifier.size(16.dp)
            ) {
                drawCheckmark(checkmarkColor)
            }
        }
    }
}

/**
 * Draws a checkmark (tick) symbol
 */
private fun DrawScope.drawCheckmark(color: Color) {
    val path = Path().apply {
        // Start point (left side of checkmark)
        moveTo(size.width * 0.25f, size.height * 0.5f)
        // Middle point (bottom of checkmark)
        lineTo(size.width * 0.45f, size.height * 0.7f)
        // End point (right side of checkmark)
        lineTo(size.width * 0.75f, size.height * 0.3f)
    }

    drawPath(
        path = path,
        color = color,
        style = androidx.compose.ui.graphics.drawscope.Stroke(
            width = 3.dp.toPx(),
            cap = StrokeCap.Round
        )
    )
}
