package com.manaknight.app.ui.screens

import Manaknight.R
import android.app.Dialog
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.IconButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.unit.dp
//import com.manaknight.app.model.remote.profitPro.ProjectDetailsRespModel
import com.manaknight.app.network.Status
import com.manaknight.app.viewmodels.BaasViewModel

import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Edit

import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.*
import androidx.compose.ui.graphics.Color

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.border
import androidx.compose.material3.*
import androidx.compose.ui.unit.sp
import androidx.compose.material.icons.filled.Close
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.navigation.NavController

import androidx.compose.foundation.background
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.text.BasicTextField
import com.manaknight.app.ui.fragments.LineItemsComposeFragmentDirections
//import androidx.compose.ui.graphics.BorderStroke
import java.time.format.DateTimeFormatter
//import androidx.compose.foundation.layout.BoxScopeInstance.align
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.navigation.fragment.NavHostFragment.Companion.findNavController
import androidx.navigation.fragment.findNavController
import com.manaknight.app.model.remote.profitPro.AllLineItemsResponseModel
import com.manaknight.app.model.remote.profitPro.JobDetailsRespModel
import com.manaknight.app.network.Resource
import com.manaknight.app.utils.CustomUtils.cacheProjectDetailsToLocal
import com.manaknight.app.utils.CustomUtils.clearCachedProjectDetails
import java.time.OffsetDateTime
import java.time.ZoneId
import java.util.concurrent.TimeUnit
import com.manaknight.app.ui.utils.isTabletLayout
import com.manaknight.app.ui.utils.getMaxContentWidth
import com.manaknight.app.ui.components.ResponsiveSheetContainer

enum class ChangeStatus {
    NO_CHANGE,
    ADDED,
    EDITED_UP,
    EDITED_DOWN
}

data class JobDetailsUIModel(
    val original: JobDetailsRespModel,
    val previous: JobDetailsRespModel? = null,
    val changeStatus: ChangeStatus
)


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LineItemsScreen(
    projectID: Int,
    baasViewModel: BaasViewModel,
    navController: NavController,
    dialog: Dialog,
    onNavigateToPreview: (Int) -> Unit,
    deleteItem:(Int)->Unit,
//    projectDetailsResource: Resource<AllLineItemsResponseModel>
) {
    val sheetState = rememberModalBottomSheetState(
        skipPartiallyExpanded = true // Ensures it fully expands instead of partially
    )
    val amountState = remember { mutableStateOf("5.00") }
    val reasonState = remember { mutableStateOf("") }
    var showCustomCreditSheet by remember { mutableStateOf(false) }

    val deletedItemIds = remember { mutableStateListOf<Int>() }
    val editedItemTimestamps = remember { mutableStateMapOf<Int, OffsetDateTime>() }
    val projectDetailsResource by baasViewModel.projectDetailsResource.observeAsState() // Observe projectDetailsResource\
    val cachedProjectDetailsResource by baasViewModel.cachedProjectDetailsResource.observeAsState()
    val editedItemChanges = remember { mutableStateMapOf<Int, Map<String, Boolean?>>() }

    val tag = "LineItemsScreenLog"
    val context = LocalContext.current

    // Extract customer name from API response
    val customerNameFromApi = projectDetailsResource?.data?.client_details?.name ?: "Customer"

    LaunchedEffect(projectID) {
        baasViewModel.setProjectId(projectID) // Trigger data fetch in ViewModel
    }

    LaunchedEffect(Unit) {
        baasViewModel.fetchProjectDetails(projectID)
    }

    Log.d("TAG", "cachedProjectDetailsResource: ${cachedProjectDetailsResource}")

    val onDeleteInitiated: (Int) -> Unit = { deletedId ->
        deletedItemIds.add(deletedId)
        deleteItem(deletedId)
//        baasViewModel.deleteLineItems(deletedId)
    }

    Surface(color = Color.White) {
        Scaffold(
            topBar = {
                TopAppBar(
                    title = {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(end = 56.dp), // Prevent overlap with potential action icons
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = customerNameFromApi,
                                textAlign = TextAlign.Center,
                                maxLines = 1
                            )
                        }
                    },
                    navigationIcon = {
                        IconButton(onClick = { navController.popBackStack() }) {
                            Icon(Icons.Filled.ArrowBack, contentDescription = "Back")
                        }
                    },
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = Color.White
                    )
                )
            },
            floatingActionButton = {}
        ) { paddingValues ->
            Surface(color = Color.White) {
                // Responsive adaptation: center and constrain main content on tablets
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                ) {
                    Column(
                        modifier = Modifier
                            .then(
                                if (isTabletLayout()) {
                                    Modifier
                                        .align(Alignment.TopCenter)
                                        .widthIn(max = getMaxContentWidth())
                                } else {
                                    Modifier.fillMaxWidth()
                                }
                            )
                            .padding(paddingValues)
                            .padding(horizontal = 16.dp)
                    ) {
                        Text(
                            text = "Change Order",
                            modifier = Modifier.weight(0.1f),
                            fontSize = 16.sp,
                            color = colorResource(R.color.profit_black),
                            fontWeight = FontWeight.Medium
                        )

                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "Line Items",
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Medium,
                                color = colorResource(R.color.profit_black)
                            )
                            // Now add the button on the "second line"
                            Button(
                                onClick = {
                                    if (projectDetailsResource?.status == Status.SUCCESS) {
                                        projectDetailsResource!!.data?.let { data ->
                                            if (baasViewModel.cachedProjectDetailsResource.value?.job_details == null) {
                                                baasViewModel.cacheProjectDetailsResource(data)
                                                cacheProjectDetailsToLocal(context, data)
                                            }
                                        }
                                    }
                                    val action =
                                        LineItemsComposeFragmentDirections.actionLineItemViewComposeToAddlineItemViewCompose(
                                            projectID = projectID,
                                            customerName = customerNameFromApi,
                                            isEdit = 1
                                        )
                                    navController.navigate(action)
                                },
                                shape = RoundedCornerShape(4.dp),
                                colors = ButtonDefaults.buttonColors(containerColor = colorResource(R.color.white)),
                                border = BorderStroke(1.dp, colorResource(R.color.profit_blue)),
                                contentPadding = PaddingValues(horizontal = 8.dp, vertical = 2.dp),
                                modifier = Modifier
                                    .height(28.dp) // Adjust height as needed
                            ) {
                                Text(
                                    text = "Add Line Item",
                                    fontSize = 16.sp,
                                    color = colorResource(R.color.profit_blue)
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Icon(
                                    painter = painterResource(id = R.drawable.ic_add),
                                    contentDescription = "Add new line item",
                                    tint = colorResource(R.color.profit_blue),
                                    modifier = Modifier.size(20.dp) // Adjust icon size as needed
                                )
                            }
                        }
                        Spacer(modifier = Modifier.height(4.dp))


                        // This Column will now take up the remaining available vertical space and scroll
                        Column(
                            modifier = Modifier
                                .weight(1f) // This makes the line items section scroll
                                .fillMaxWidth()
                        ) {


                            when (projectDetailsResource?.status) {
                                Status.LOADING -> {
                                    dialog.show()
                                    //                    Text("Loading line items...")
                                }

                                Status.SUCCESS -> {
                                    dialog.dismiss()

                                    val projectDetails = projectDetailsResource!!.data
                                    val cachedDetails = cachedProjectDetailsResource
                                    val lineItems = remember(projectDetails?.job_details, cachedDetails?.job_details) {
                                        compareLineItems(
                                            projectDetails?.job_details ?: emptyList(),
                                            cachedDetails?.job_details
                                        )
                                    }


                                    if (lineItems.isNullOrEmpty()) {
                                        Spacer(modifier = Modifier.weight(1f))
                                        Text("You haven't added any line items. Please add a line item to proceed.")
                                        Spacer(modifier = Modifier.weight(1f))
                                    } else {
                                        LazyColumn(verticalArrangement = Arrangement.spacedBy(8.dp)) {

                                            itemsIndexed(
                                                items = lineItems,
                                                key = { index, item ->
                                                    item.original.line_id ?: index
                                                }) { index, uiModel ->
                                                val item = uiModel.original
                                                Log.d(
                                                    "TAG",
                                                    "LineItemsScreen cachedDetails: ${uiModel.changeStatus}"
                                                )
                                                GenericLineItem(
                                                    index = index + 1,
                                                    description = item.description ?: "",
                                                    salePrice = "$${item.sale_price?.toInt() ?: 0}",
                                                    salePriceOld = "$${uiModel.previous?.sale_price?.toInt() ?: null}",
                                                    laborBudget = "$${item.labour_budget?.toInt() ?: 0}",
                                                    laborBudgetOld = "$${uiModel.previous?.labour_budget?.toInt() ?: null}",
                                                    profitOverhead = "$${item.profit_overhead_amount?.toInt() ?: 0}",
                                                    profitOverheadOld = "$${uiModel.previous?.profit_overhead_amount?.toInt() ?: null}",
                                                    materialBudget = "$${item.material_budget?.toInt() ?: 0}",
                                                    materialBudgetOld = "$${uiModel.previous?.material_budget?.toInt() ?: null}",
                                                    updatedAt = item.update_at ?: "",
                                                    typeLabel = when (item.estimated_by) {
                                                        "square_foot" -> "Square Foot"
                                                        "lineal_foot" -> "Linear Foot"
                                                        else -> "Material & Labor"
                                                    },
                                                    isNewItem = item.isNewItem ?: false,
                                                    isUpdatedItem = editedItemTimestamps.containsKey(
                                                        item.line_id
                                                    ),
                                                    isDeletedItem = deletedItemIds.contains(item.line_id),
                                                    changeStatus = uiModel.changeStatus,
                                                    onEditClick = {

                                                        if (projectDetailsResource?.status == Status.SUCCESS) {
                                                            projectDetailsResource!!.data?.let { data ->
                                                                if (cachedProjectDetailsResource?.job_details == null) {
                                                                    baasViewModel.cacheProjectDetailsResource(data)
                                                                }
                                                            }
                                                        }
                                                        val nonNullDescription = item.description ?: ""
                                                        val nonNullEstimatedBy = item.estimated_by ?: ""
                                                        val nonNullUpdateAt = item.update_at ?: ""

                                                        // Navigate to edit screen, passing necessary data
                                                        val action =
                                                            LineItemsComposeFragmentDirections.actionLineItemViewComposeToEditlineItemViewCompose(
                                                                projectID = projectID,
                                                                customerName = customerNameFromApi,
                                                                lineItemID = item.line_id ?: 0,
                                                                initialDescription = item.description
                                                                    ?: "",
                                                                initialType = item.estimated_by ?: "",
                                                                item = item.copy(
                                                                    description = nonNullDescription,
                                                                    estimated_by = nonNullEstimatedBy,
                                                                    update_at = nonNullUpdateAt
                                                                )
                                                            )
                                                        navController.navigate(action)

                                                    },
                                                    onDeleteClick = {
                                                        item.line_id?.let {
                                                            if (projectDetailsResource?.status == Status.SUCCESS) {
                                                                projectDetailsResource!!.data?.let { data ->
                                                                    if (cachedProjectDetailsResource?.job_details == null) {
                                                                        baasViewModel.cacheProjectDetailsResource(data)
                                                                    }
                                                                }
                                                            }

                                                            onDeleteInitiated(it)
                                                        }
                                                    }
                                                )
                                            }


                                        }
                                    }
                                }

                                Status.ERROR -> {
                                    dialog.dismiss()
                                    //                    Text("Error loading line items: ${projectDetailsResource!!.message}")
                                }

                                else -> {}
                            }
                        }
                        // Remove horizontal padding for TotalSummary to allow full-width shadow
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 8.dp)
                        ) {

                            val cachedDetails = cachedProjectDetailsResource
                            val prevItemsTotal = cachedDetails?.totals

                            projectDetailsResource?.data?.totals?.let { totals ->
                                TotalSummary(
                                    totalSalePrice = "$${totals.sale_price?.toInt() ?: 0}",
                                    previousTotalSalePrice = "$${prevItemsTotal?.sale_price?.toInt() }",
                                    totalProfitOverhead = "$${totals.total_profit_overhead?.toInt() ?: 0}",
                                    previousTotalProfitOverhead = "$${prevItemsTotal?.total_profit_overhead?.toInt() }",
                                    totalMaterialBudget = "$${totals.material_budget?.toInt() ?: 0}",
                                    previousTotalMaterialBudget = "$${prevItemsTotal?.material_budget?.toInt() }",
                                    totalLaborBudget = "$${totals.labour_budget?.toInt() ?: 0}",
                                    previousTotalLaborBudget = "$${prevItemsTotal?.labour_budget?.toInt()}",
                                    onSaveAsDraftClick = {
                                        baasViewModel.cacheProjectDetailsResource(null)
                                        clearCachedProjectDetails(context)
                                        navController.popBackStack(
                                            R.id.home,
                                            false
                                        )
                                    },
                                    onSaveAndReviewClick = {
                                        onNavigateToPreview(projectID)
                                    },
                                    onclick = { showCustomCreditSheet = true },
                                )
                            }
                        }
                    }

                }

            }

            ResponsiveSheetContainer(
                showSheet = showCustomCreditSheet,
                onDismiss = { showCustomCreditSheet = false },
                sheetState = sheetState,
                headerContent = {
                    CustomCreditSheetHeader(
                        customerName = customerNameFromApi,
                        amountState = amountState,
                        reasonState = reasonState,
                        onDismiss = { showCustomCreditSheet = false },
                        onSave = { amount, reason ->
                            onSaveCredit(amount, reason)
                            showCustomCreditSheet = false
                        }
                    )
                },
                content = {
                    CustomCreditSheetContentBody(
                        amountState = amountState,
                        reasonState = reasonState
                    )
                }
            )

        }

    }


}


fun compareLineItems(
    newItems: List<JobDetailsRespModel>,
    oldItems: List<JobDetailsRespModel>?
): List<JobDetailsUIModel> {
    if (oldItems == null) {
        return newItems.map { JobDetailsUIModel(it, null, ChangeStatus.NO_CHANGE) }
    }

    return newItems.map { newItem ->
        val oldItem = oldItems.find { it.line_id == newItem.line_id }

        if (oldItem == null) {
            return@map JobDetailsUIModel(newItem, null, ChangeStatus.ADDED)
        }

        val saleChanged = newItem.sale_price != oldItem.sale_price
        val labourChanged = newItem.labour_budget != oldItem.labour_budget
        val materialChanged = newItem.material_budget != oldItem.material_budget
        val profitChanged = newItem.profit_overhead_amount != oldItem.profit_overhead_amount
        val estimatedChanged = newItem.estimated_by != oldItem.estimated_by
        val descriptionChanged = newItem.description != oldItem.description

        val status = when {
            saleChanged && newItem.sale_price != null && oldItem.sale_price != null -> {
                if (newItem.sale_price > oldItem.sale_price) ChangeStatus.EDITED_UP
                else ChangeStatus.EDITED_DOWN
            }
            labourChanged || materialChanged || profitChanged || estimatedChanged || descriptionChanged -> ChangeStatus.EDITED_DOWN
            else -> ChangeStatus.NO_CHANGE
        }

        JobDetailsUIModel(newItem, oldItem, status)
    }
}








@Composable
fun GenericLineItem(
    index: Int,
    description: String,
    salePrice: String,
    salePriceOld: String?,
    laborBudget: String,
    laborBudgetOld: String?,
    profitOverhead: String,
    profitOverheadOld: String?,
    materialBudget: String,
    materialBudgetOld: String?,
    typeLabel: String,
    updatedAt: String,
    isNewItem: Boolean = false,
    isUpdatedItem: Boolean = false,
    isDeletedItem: Boolean = false,
    onEditClick: () -> Unit,
    onDeleteClick: () -> Unit,
    changeStatus: ChangeStatus
) {
    val backgroundColor = colorResource(id = R.color.white)
    val newItemColor = colorResource(id = R.color.green)
    val updatedItemColor = colorResource(id = R.color.blue) // Or any color to indicate update
    val deletedItemColor = colorResource(id = R.color.redgradstart)
    val defaultTextColor = colorResource(id = R.color.profit_black)
    val subTextColor = colorResource(id = R.color.text_sub)
    val blueTextColor = colorResource(id = R.color.profit_blue)
    val blueBgColor = colorResource(id = R.color.project_blue_bg)

    val borderColor = when (changeStatus) {
        ChangeStatus.ADDED -> Color(0xFF4CAF50)  // Green for added
        ChangeStatus.EDITED_UP -> Color(0xFF4CAF50)  // Green for edited up
        ChangeStatus.EDITED_DOWN -> Color(0xFFF44336)  // Red for edited down
        else -> Color.LightGray  // Default for no change
    }

    val textColor = when (changeStatus) {
        ChangeStatus.ADDED -> Color(0xFF4CAF50)  // Green for added
        ChangeStatus.EDITED_UP -> Color(0xFF4CAF50)  // Green for edited up
        ChangeStatus.EDITED_DOWN -> Color(0xFFF44336)  // Red for edited down
        else -> defaultTextColor  // Default text color
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(backgroundColor, RoundedCornerShape(8.dp))
            .border(
                BorderStroke(
                    1.dp,
                    if (isDeletedItem) deletedItemColor else if (isNewItem) newItemColor else if (isUpdatedItem) updatedItemColor else borderColor
                ),
                RoundedCornerShape(8.dp)
            )
            .padding(horizontal = 16.dp, vertical = 12.dp)
            .alpha(if (isDeletedItem) 0.7f else 1f) // Slightly fade out deleted items
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(
                text = "${index}. ${description}",
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = textColor,
                modifier = Modifier.weight(1f)
            )
            Row {
                IconButton(onClick = onEditClick, enabled = !isDeletedItem) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_edit_icon), // Replace with your actual icon
                        tint = colorResource(R.color.text_sub),
                        contentDescription = "Edit ",
                        modifier = Modifier.size(20.dp)
                    )

                }
                IconButton(onClick = onDeleteClick, enabled = !isDeletedItem) {

                    Icon(
                        painter = painterResource(id = R.drawable.ic_delete_icon), // Replace with your actual icon
                        tint = colorResource(R.color.text_sub),
                        contentDescription = "Delete",
                        modifier = Modifier.size(28.dp)
                    )
                     }
            }
        }
        Spacer(modifier = Modifier.height(8.dp))
        InfoRow(
            label = "Sale Price",
            newValue = salePrice,
            oldValue = salePriceOld,
            isNew = isNewItem,
            isUpdated = isUpdatedItem,
            isDeleted = isDeletedItem,
            changeStatus = changeStatus
        )
        Spacer(modifier = Modifier.height(4.dp))
        InfoRow(
            label = "Labor Budget",
            newValue = laborBudget,
            oldValue = laborBudgetOld,
            isNew = isNewItem,
            isUpdated = isUpdatedItem,
            isDeleted = isDeletedItem,
            changeStatus = changeStatus
        )
        Spacer(modifier = Modifier.height(4.dp))
        InfoRow(
            label = "Profit Overhead",
            newValue = profitOverhead,
            oldValue = profitOverheadOld,
            isNew = isNewItem,
            isUpdated = isUpdatedItem,
            isDeleted = isDeletedItem,
            changeStatus = changeStatus
        )
        Spacer(modifier = Modifier.height(4.dp))
        InfoRow(
            label = "Material Budget",
            newValue = materialBudget,
            oldValue = materialBudgetOld,
            isNew = isNewItem,
            isUpdated = isUpdatedItem,
            isDeleted = isDeletedItem,
            changeStatus = changeStatus
        )
        Spacer(modifier = Modifier.height(12.dp))

        Divider(color = colorResource(R.color.stroke_soft), thickness = 1.dp)
        Spacer(modifier = Modifier.height(12.dp))
        Text(
            text = typeLabel,
            fontSize = 12.sp,
            fontWeight = FontWeight.Medium,
            color = blueTextColor,
            modifier = Modifier
                .background(blueBgColor, RoundedCornerShape(4.dp))
                .padding(horizontal = 8.dp, vertical = 4.dp)
        )
    }
}


@Composable
fun InfoRow(
    label: String,
    newValue: String,
    oldValue: String? = null,
    isNew: Boolean,
    isUpdated: Boolean,
    isDeleted: Boolean,
    changeStatus: ChangeStatus
) {
    val defaultTextColor = colorResource(id = R.color.profit_black)
    val newItemColor = colorResource(id = R.color.green)
    val updatedItemColor = colorResource(id = R.color.blue)
    val deletedItemColor = colorResource(id = R.color.redgradstart)
    val subTextColor = colorResource(id = R.color.text_sub)

    val textColor = when (changeStatus) {
        ChangeStatus.ADDED -> Color(0xFF4CAF50)  // Green for added
        ChangeStatus.EDITED_UP -> Color(0xFF4CAF50)  // Green for edited up
        ChangeStatus.EDITED_DOWN -> Color(0xFFF44336)  // Red for edited down
        else -> when {
            isDeleted -> deletedItemColor
            isNew -> newItemColor
            isUpdated -> updatedItemColor
            else -> defaultTextColor
        }
    }

    val newNum = newValue.replace(Regex("[^\\d.]"), "").toDoubleOrNull()
    val oldNum = oldValue?.replace(Regex("[^\\d.]"), "")?.toDoubleOrNull()
    Log.d("TAG", "InfoRow: ${oldValue} ${oldNum}  ${newNum}")

    val showDiff = oldNum != null && oldNum != newNum
    val diffText = if (showDiff) {

        if (newNum != null && oldNum != null) {
            val diff = newNum - oldNum
            val prefix = if (diff > 0) "+" else ""
            "($prefix${"%.2f".format(diff)})"
        } else null
    } else null

    Column(modifier = Modifier.fillMaxWidth()) {
        Row(
            horizontalArrangement = Arrangement.SpaceBetween,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(label, fontSize = 14.sp, color = subTextColor)

            if (showDiff && !isDeleted && oldValue != null && diffText != null) {
                Row(
                    horizontalArrangement = Arrangement.End,
//                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        newValue,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = textColor
                    )
                    Text(
                        text = " $diffText",
                        fontSize = 12.sp,
                        color = textColor,
                        fontStyle = FontStyle.Italic
                    )
                }
            }
            else{
                Text(
                    newValue,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = defaultTextColor
                )
            }
        }

    }
}



@Composable
fun TotalSummary(
    totalSalePrice: String,
    previousTotalSalePrice: String? = null,
    totalProfitOverhead: String,
    previousTotalProfitOverhead: String? = null,
    totalMaterialBudget: String,
    previousTotalMaterialBudget: String? = null,
    totalLaborBudget: String,
    previousTotalLaborBudget: String? = null,
    onSaveAsDraftClick: () -> Unit,
    onSaveAndReviewClick: () -> Unit,
    onclick: () -> Unit,
) {

    Box(
        modifier = Modifier.fillMaxWidth()
    ) {
        // Full-width top shadow that extends beyond content padding
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(12.dp) // Increased height for a stronger shadow
                .background(
                    Brush.verticalGradient(
                        colors = listOf(
                            Color(0xCC000000), // Much more opaque black
                            Color.Transparent
                        )
                    )
                )
        )

        // Content with white background
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(colorResource(id = R.color.white))
                .padding(horizontal = 16.dp, vertical = 12.dp)
        ) {


            TotalRow("Total Sale Price", totalSalePrice, previousTotalSalePrice)
            Spacer(modifier = Modifier.height(8.dp))
            TotalRow("Total Profit Overhead", totalProfitOverhead, previousTotalProfitOverhead)
            Spacer(modifier = Modifier.height(8.dp))
            TotalRow("Total Material Budget", totalMaterialBudget, previousTotalMaterialBudget)
            Spacer(modifier = Modifier.height(8.dp))
            TotalRow("Total Labor Budget", totalLaborBudget, previousTotalLaborBudget)
            Spacer(modifier = Modifier.height(16.dp))


            Row(
                modifier = Modifier
                    .fillMaxWidth(),
                horizontalArrangement = Arrangement.End
            ) {
                Button(
                    onClick = onclick,
                    shape = RoundedCornerShape(8.dp),
                    colors = ButtonDefaults.buttonColors(containerColor = colorResource(R.color.white)),
                    border = BorderStroke(1.dp, colorResource(R.color.black)),
                    contentPadding = PaddingValues(horizontal = 10.dp, vertical = 2.dp),
                    modifier = Modifier
                        .wrapContentWidth() // Make the button take the full width
                        .height(28.dp) // Adjust height as needed
                ) {
                    Text(
                        text = "Custom Credit",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = colorResource(R.color.profit_black)
                    )

                }

            }
            Spacer(modifier = Modifier.height(16.dp))
            Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
                Button(
                    onClick = onSaveAsDraftClick,
                    modifier = Modifier.weight(1f),
                    shape = RoundedCornerShape(8.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color.White,
                        contentColor = Color.Black
                    ),
                    border = BorderStroke(1.dp, Color.Black)
                ) {
                    Text("Discard", fontWeight = FontWeight.SemiBold, fontSize = 16.sp)
                }
                Button(
                    onClick = onSaveAndReviewClick,
                    modifier = Modifier.weight(1f),
                    shape = RoundedCornerShape(8.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color.Black,
                        contentColor = Color.White
                    )
                ) {
                    Text("Save & Review", fontWeight = FontWeight.SemiBold, fontSize = 16.sp)
                }
            }
        }
    }
}

@Composable
fun TotalRow(label: String, newValue: String, oldValue: String?) {

    val new = newValue.replace(Regex("[^\\d.]"), "").toDoubleOrNull()
    val old = oldValue?.replace(Regex("[^\\d.]"), "")?.toDoubleOrNull()
    Log.d("TAG", "TotalRow: ${old}   ${new}")
    val hasChanged = old != null && new != old
    val changeAmount = if (hasChanged) {
        if (new != null && old != null){
            new.toDouble() - old.toDouble()
        }
        else{
            0.0
        }
    } else 0.0

    val changeColor = when {
        changeAmount > 0 -> Color(0xFF4CAF50) // green
        changeAmount < 0 -> Color(0xFFF44336) // red
        else -> Color.Black
    }

    Row(
        horizontalArrangement = Arrangement.SpaceBetween,
        modifier = Modifier.fillMaxWidth()
    ) {
        Text(label, fontSize = 14.sp, color = colorResource(id = R.color.gray))
        Row {
            Text(
                text = newValue,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium
            )
            if (hasChanged && changeAmount != 0.0) {
                Spacer(modifier = Modifier.width(4.dp))
                Text(
                    text = if (changeAmount > 0) "+$${changeAmount.toInt()}" else "-$${-changeAmount.toInt()}",
                    fontSize = 14.sp,
                    color = changeColor,
                    fontWeight = FontWeight.SemiBold
                )
            }
        }
    }
}


@Composable
fun CustomCreditSheetContent(
    onDismiss: () -> Unit,
    onSave: (amount: Double?, reason: String) -> Unit,
    customerName: String,
    amountState: MutableState<String>,
    reasonState: MutableState<String>
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
            .heightIn(min = 741.dp)
    ) {
        // Top Bar
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onDismiss) {
                Icon(Icons.Filled.Close, contentDescription = "Close")
            }
            Text(text = "$customerName", fontSize = 18.sp, fontWeight = FontWeight.Medium, color = colorResource(R.color.black))
            Button(onClick = {
                val enteredAmount = amountState.value.toDoubleOrNull()
                onSave(enteredAmount, reasonState.value)
            },
                shape = RoundedCornerShape(4.dp),
                colors = ButtonDefaults.buttonColors(containerColor = colorResource(R.color.white)),
                border = BorderStroke(1.dp, colorResource(R.color.profit_blue)),
                contentPadding = PaddingValues(horizontal = 8.dp, vertical = 2.dp),
                modifier = Modifier
                    .height(28.dp) // Adjust height as needed
                     )
                         {
                Text("Save",fontSize = 16.sp,
                    color = colorResource(R.color.profit_blue))
            }
        }
        Spacer(modifier = Modifier.height(16.dp))


        Text("Custom Credit", fontWeight = FontWeight.Medium, fontSize = 16.sp, color = colorResource(R.color.black))

        Spacer(modifier = Modifier.height(16.dp))

        // Amount Input
        Text("Amount", fontWeight = FontWeight.Medium, fontSize = 14.sp, color = colorResource(R.color.black))
        Spacer(modifier = Modifier.height(4.dp))

        BasicTextField(
            value = amountState.value,
            onValueChange = {
                if (it.matches(Regex("^\\d*\\.?\\d*\$"))) {
                    amountState.value = it
                }
            },
            modifier = Modifier
                .fillMaxWidth()
                .border(1.dp, colorResource(R.color.stroke_soft), RoundedCornerShape(4.dp))
                .padding(8.dp),
            textStyle = TextStyle(fontSize = 16.sp),
            decorationBox = { innerTextField ->
                Row(
                    Modifier
                        .fillMaxWidth()
                        .padding(8.dp)
                ) {
                    Text(text = "$")
                    Spacer(modifier = Modifier.width(4.dp))
                    Box(Modifier.weight(1f)) {
                        innerTextField()
                    }
                }
            }
        )
        Spacer(modifier = Modifier.height(16.dp))

        // Reason for adjustment Input
        Text("Reason for adjustment", fontWeight = FontWeight.Medium, fontSize = 14.sp, color = colorResource(R.color.black))
        Spacer(modifier = Modifier.height(4.dp))
        OutlinedTextField(
            value = reasonState.value,
            onValueChange = { reasonState.value = it },
            modifier = Modifier
                .fillMaxWidth()
                .height(120.dp)
        )
    }
}

@Composable
fun CustomCreditSheetHeader(
    customerName: String,
    amountState: MutableState<String>,
    reasonState: MutableState<String>,
    onDismiss: () -> Unit,
    onSave: (amount: Double?, reason: String) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        IconButton(onClick = onDismiss) {
            Icon(Icons.Filled.Close, contentDescription = "Close")
        }
        Text(
            text = customerName,
            fontSize = 18.sp,
            fontWeight = FontWeight.Medium,
            color = colorResource(R.color.black)
        )
        Button(
            onClick = {
                val enteredAmount = amountState.value.toDoubleOrNull()
                onSave(enteredAmount, reasonState.value)
            },
            shape = RoundedCornerShape(4.dp),
            colors = ButtonDefaults.buttonColors(containerColor = colorResource(R.color.white)),
            border = BorderStroke(1.dp, colorResource(R.color.profit_blue)),
            contentPadding = PaddingValues(horizontal = 8.dp, vertical = 2.dp),
            modifier = Modifier.height(28.dp)
        ) {
            Text("Save", fontSize = 16.sp, color = colorResource(R.color.profit_blue))
        }
    }
}

@Composable
fun CustomCreditSheetContentBody(
    amountState: MutableState<String>,
    reasonState: MutableState<String>
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        Text("Custom Credit", fontWeight = FontWeight.Medium, fontSize = 16.sp, color = colorResource(R.color.black))

        Spacer(modifier = Modifier.height(16.dp))

        // Amount Input
        Text("Amount", fontWeight = FontWeight.Medium, fontSize = 14.sp, color = colorResource(R.color.black))
        Spacer(modifier = Modifier.height(4.dp))

        BasicTextField(
            value = amountState.value,
            onValueChange = {
                if (it.matches(Regex("^\\d*\\.?\\d*\$"))) {
                    amountState.value = it
                }
            },
            modifier = Modifier
                .fillMaxWidth()
                .border(1.dp, colorResource(R.color.stroke_soft), RoundedCornerShape(4.dp))
                .padding(8.dp),
            textStyle = TextStyle(fontSize = 16.sp),
            decorationBox = { innerTextField ->
                Row(
                    Modifier
                        .fillMaxWidth()
                        .padding(8.dp)
                ) {
                    Text(text = "$")
                    Spacer(modifier = Modifier.width(4.dp))
                    Box(Modifier.weight(1f)) {
                        innerTextField()
                    }
                }
            }
        )
        Spacer(modifier = Modifier.height(16.dp))

        // Reason for adjustment Input
        Text("Reason for adjustment", fontWeight = FontWeight.Medium, fontSize = 14.sp, color = colorResource(R.color.black))
        Spacer(modifier = Modifier.height(4.dp))
        OutlinedTextField(
            value = reasonState.value,
            onValueChange = { reasonState.value = it },
            modifier = Modifier
                .fillMaxWidth()
                .height(120.dp)
        )
    }
}

// Example onSaveCredit implementation (replace with your actual logic)
fun onSaveCredit(amount: Double?, reason: String) {
    println("Saving custom credit: Amount = $amount, Reason = $reason")
    // Your actual saving logic here
}