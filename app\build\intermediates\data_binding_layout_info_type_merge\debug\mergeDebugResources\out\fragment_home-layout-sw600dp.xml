<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_home" modulePackage="Manaknight" filePath="app\src\main\res\layout-sw600dp\fragment_home.xml" directory="layout-sw600dp" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout-sw600dp/fragment_home_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="677" endOffset="51"/></Target><Target id="@+id/headerInclude" tag="layout-sw600dp/fragment_home_0" include="header"><Expressions/><location startLine="8" startOffset="4" endLine="12" endOffset="45"/></Target><Target id="@+id/container" view="RelativeLayout"><Expressions/><location startLine="14" startOffset="4" endLine="654" endOffset="20"/></Target><Target id="@+id/innerConstraintLayout" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="24" startOffset="8" endLine="653" endOffset="59"/></Target><Target id="@+id/scrollTotalProfitOverhead" view="HorizontalScrollView"><Expressions/><location startLine="87" startOffset="36" endLine="108" endOffset="58"/></Target><Target id="@+id/txtTotalProfitOverhead" view="TextView"><Expressions/><location startLine="92" startOffset="40" endLine="107" endOffset="79"/></Target><Target id="@+id/scrollCompanyHealth" view="HorizontalScrollView"><Expressions/><location startLine="147" startOffset="36" endLine="167" endOffset="58"/></Target><Target id="@+id/txtCompanyHealth" view="TextView"><Expressions/><location startLine="152" startOffset="40" endLine="166" endOffset="79"/></Target><Target id="@+id/btnViewDetails" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="178" startOffset="20" endLine="192" endOffset="53"/></Target><Target id="@+id/txtTotalContracts" view="TextView"><Expressions/><location startLine="221" startOffset="32" endLine="236" endOffset="71"/></Target><Target id="@+id/txtTotalAR" view="TextView"><Expressions/><location startLine="258" startOffset="32" endLine="273" endOffset="71"/></Target><Target id="@+id/txtMaterialBalance" view="TextView"><Expressions/><location startLine="305" startOffset="32" endLine="320" endOffset="71"/></Target><Target id="@+id/txtMaterialSpent" view="TextView"><Expressions/><location startLine="342" startOffset="32" endLine="357" endOffset="71"/></Target><Target id="@+id/txtLaborBalance" view="TextView"><Expressions/><location startLine="388" startOffset="32" endLine="403" endOffset="71"/></Target><Target id="@+id/txtLaborSpent" view="TextView"><Expressions/><location startLine="425" startOffset="32" endLine="440" endOffset="71"/></Target><Target id="@+id/firstRow" view="LinearLayout"><Expressions/><location startLine="466" startOffset="24" endLine="548" endOffset="38"/></Target><Target id="@+id/draftsCard" view="LinearLayout"><Expressions/><location startLine="473" startOffset="28" endLine="508" endOffset="42"/></Target><Target id="@+id/txtDraftsCount" view="TextView"><Expressions/><location startLine="482" startOffset="32" endLine="496" endOffset="71"/></Target><Target id="@+id/outstandingCard" view="LinearLayout"><Expressions/><location startLine="512" startOffset="28" endLine="547" endOffset="42"/></Target><Target id="@+id/txtOutstandingCount" view="TextView"><Expressions/><location startLine="521" startOffset="32" endLine="535" endOffset="71"/></Target><Target id="@+id/activeCard" view="LinearLayout"><Expressions/><location startLine="561" startOffset="28" endLine="596" endOffset="42"/></Target><Target id="@+id/txtActiveCount" view="TextView"><Expressions/><location startLine="570" startOffset="32" endLine="584" endOffset="71"/></Target><Target id="@+id/completedCard" view="LinearLayout"><Expressions/><location startLine="600" startOffset="28" endLine="635" endOffset="42"/></Target><Target id="@+id/txtCompletedCount" view="TextView"><Expressions/><location startLine="609" startOffset="32" endLine="623" endOffset="71"/></Target><Target id="@+id/addNewEstimation" view="ImageView"><Expressions/><location startLine="639" startOffset="24" endLine="647" endOffset="66"/></Target></Targets></Layout>