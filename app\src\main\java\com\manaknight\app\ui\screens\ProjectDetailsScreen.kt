import Manaknight.R
import android.app.Dialog
import android.util.Log
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.foundation.border
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.navigation.NavController
import com.manaknight.app.model.remote.profitPro.DrawsRespModel
import com.manaknight.app.model.remote.profitPro.JobDetailsRespModel
import com.manaknight.app.network.Status
import com.manaknight.app.ui.screens.BottomSheetContent
//import androidx.lifecycle.viewmodel.compose.viewModel
import com.manaknight.app.viewmodels.BaasViewModel
import kotlinx.coroutines.launch
import com.manaknight.app.ui.utils.isTabletLayout
import com.manaknight.app.ui.utils.getMaxContentWidth
import com.manaknight.app.ui.utils.getResponsiveHorizontalPadding
import com.manaknight.app.ui.components.ResponsiveSheetContainer


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProjectDetailsScreen(projectId: Int, baasViewModel: BaasViewModel, onTrackingClick: (String) -> Unit, dialog:Dialog,navController: NavController,onNavigateToLineItems: (Int, String) -> Unit) {
    var showJobDetailsSheet by remember { mutableStateOf(false) }
    val sheetState = rememberModalBottomSheetState()
    var selectedJob by remember { mutableStateOf<JobDetailsRespModel?>(null) }
//    val sheetState = rememberModalBottomSheetState()
    val scope = rememberCoroutineScope()
    var showBottomSheet by remember { mutableStateOf(false) }
    val isTablet = isTabletLayout()

    LaunchedEffect(projectId) {
        baasViewModel.setProjectId(projectId)
    }

    val projectDetailsResource by baasViewModel.projectDetailsResource.observeAsState()

    projectDetailsResource?.let { resource ->
        when (resource?.status) {
            Status.LOADING  -> {
//                CircularProgressIndicator()
                dialog.show()
            }

            Status.SUCCESS -> {
                dialog.hide()
                val projectDetails = resource.data

                projectDetails?.let { details ->
                    Surface(color = Color.White) {
                        Column(
                            modifier = Modifier
                                .fillMaxSize()
                                .background(Color.White)
                        ) {
                            // Top bar (header) - always full width
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                IconButton(onClick = {
                                    navController.popBackStack()
                                }) {
                                    Icon(Icons.Default.ArrowBack, contentDescription = "Back")
                                }
                                Text(
                                    text = details.client_details.name ?: "Project Name",
                                    fontWeight = FontWeight.Medium,
                                    fontSize = 16.sp
                                )
                                IconButton(onClick = { showBottomSheet = true }) {
                                    Icon(
                                        painter = painterResource(id = R.drawable.ic_options_bold),
                                        contentDescription = "More",
                                        tint = colorResource(R.color.black),
                                        modifier = Modifier.size(16.dp)
                                    )
                                }
                            }

                            Spacer(modifier = Modifier.height(16.dp))

                            val isTablet = isTabletLayout()

//                            Row(
//
//                                modifier = Modifier
//                                    .then(
//                                        if (!isTablet) Modifier
//                                            .padding(horizontal = 16.dp)
//                                        else Modifier
//                                    )
////                                        else Modifier.padding(horizontal = 16.dp))
//
//                            ){


                            if (isTablet) {
                                Box(modifier = Modifier.widthIn(max = 500.dp)) {
                                    Icon(
                                        painter = painterResource(id = R.drawable.ic_options_bold),
                                        contentDescription = "More",
                                        tint = colorResource(R.color.white),
                                        modifier = Modifier.size(16.dp)
                                    )

                            Button(
                                onClick = { onTrackingClick(details.client_details.name ?: "Project Name") },
                                shape = RoundedCornerShape(4.dp),
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = colorResource(
                                        R.color.white
                                    )
                                ),
                                border = BorderStroke(1.dp, colorResource(R.color.profit_blue)),
                                contentPadding = PaddingValues(
                                    horizontal = 8.dp,
                                    vertical = 2.dp
                                ),
                                modifier = Modifier
                                    .align(Alignment.CenterEnd)
                                    .padding(2.dp)
                                    .height(28.dp)
                                    .wrapContentWidth()
                                    .defaultMinSize(minWidth = 0.dp)
                            ) {
                                Text(
                                    "Tracking", fontSize = 16.sp,
                                    color = colorResource(R.color.profit_blue)
                                )
                                Spacer(modifier = Modifier.width(4.dp))
                                Icon(
                                    painter = painterResource(id = R.drawable.ic_arrow_right),
                                    contentDescription = "Track",
                                    tint = colorResource(R.color.profit_blue),
                                    modifier = Modifier.size(16.dp)
                                )
                            }}}
                            else{
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.End // Aligns content to the end
                                ) {
                                Button(
                                    onClick = { onTrackingClick(details.client_details.name ?: "Project Name") },
                                    shape = RoundedCornerShape(4.dp),
                                    colors = ButtonDefaults.buttonColors(
                                        containerColor = colorResource(
                                            R.color.white
                                        )
                                    ),
                                    border = BorderStroke(1.dp, colorResource(R.color.profit_blue)),
                                    contentPadding = PaddingValues(
                                        horizontal = 8.dp,
                                        vertical = 2.dp
                                    ),
                                    modifier = Modifier
//                                        .align(Alignment.End)
                                        .padding(2.dp)
                                        .height(28.dp)
                                        .wrapContentWidth()
                                        .defaultMinSize(minWidth = 0.dp)
                                ) {
                                    Text(
                                        "Tracking", fontSize = 16.sp,
                                        color = colorResource(R.color.profit_blue)
                                    )
                                    Spacer(modifier = Modifier.width(4.dp))
                                    Icon(
                                        painter = painterResource(id = R.drawable.ic_arrow_right),
                                        contentDescription = "Track",
                                        tint = colorResource(R.color.profit_blue),
                                        modifier = Modifier.size(16.dp)
                                    )
                                }
                            }}

                            Spacer(modifier = Modifier.height(16.dp))

                            // Main content: center/constrain on tablet, full width on phone
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                            ) {
                                Column(
                                    modifier = Modifier
                                        .then(
                                            if (isTablet) Modifier
                                                .align(Alignment.Center)
                                                .widthIn(max = getMaxContentWidth())
                                                .padding(horizontal = getResponsiveHorizontalPadding())
                                            else Modifier.fillMaxWidth()
                                        )
                                        .verticalScroll(rememberScrollState())
                                ) {
                                    // ... main content (cards, lists, etc.) ...

//                        Client details

                                    Card(
                                    modifier = Modifier.fillMaxWidth(),
                                    colors = CardDefaults.cardColors(Color.White),
                                    border = BorderStroke(1.dp, color = colorResource(R.color.stroke_soft)),
                                    shape = RoundedCornerShape(12.dp),
                                ) {
                                    Column(
                                        modifier = Modifier.padding(16.dp),
                                        verticalArrangement = Arrangement.spacedBy(8.dp)
                                    ) {
                                        Text(
                                            "Client details",
                                            color = colorResource(R.color.text_sub),
                                            fontWeight = FontWeight.Medium
                                        )

                                        Text(
                                            details.client_details.name ?: "Client Name",
                                            fontWeight = FontWeight.Medium,
                                            color = colorResource(R.color.profit_black)
                                        )
                                        Text(
                                            details.client_details.email ?: "<EMAIL>",
                                            color = colorResource(R.color.profit_black)
                                        )
                                        Text(
                                            details.client_details.address ?: "24, street, luton road",
                                            color = colorResource(R.color.profit_black)
                                        )
                                    }
                                }

                                Spacer(modifier = Modifier.height(12.dp))

//                        job details
                                Card(
                                    modifier = Modifier.fillMaxWidth(),
                                    colors = CardDefaults.cardColors(Color.White),
                                    border = BorderStroke(1.dp, color = colorResource(R.color.stroke_soft)),
                                    shape = RoundedCornerShape(12.dp),
                                ) {
                                    Column() {
                                        Text(
                                            "Job Details",
                                            Modifier.padding(16.dp),
                                            fontWeight = FontWeight.Medium,
                                            color = colorResource(R.color.profit_black)
                                        )


                                        Spacer(modifier = Modifier.height(12.dp))
                                        details.job_details.forEach { job ->
                                            Row(
                                                modifier = Modifier
                                                    .fillMaxWidth()
                                                    .height(IntrinsicSize.Min), // to align borders by height

                                                verticalAlignment = Alignment.CenterVertically
                                            ) {
                                                // Cell 1
                                                Box(
                                                    modifier = Modifier
                                                        .weight(1f)
                                                        .fillMaxHeight()
                                                        .border(
                                                            0.5.dp,
                                                            color = colorResource(R.color.stroke_light)
                                                        )
                                                        .padding(8.dp)
                                                        .align(Alignment.CenterVertically),
                                                    contentAlignment = Alignment.CenterStart
                                                ) {
                                                    Text(
                                                        text = "#${job.line_id}" ?: "100",
                                                        color = colorResource(R.color.text_sub)
                                                    )
                                                }

                                                // Cell 2
                                                Box(
                                                    modifier = Modifier
                                                        .weight(2f)
                                                        .fillMaxHeight()
                                                        .border(
                                                            0.5.dp,
                                                            color = colorResource(R.color.stroke_light)
                                                        )
                                                        .padding(8.dp)
                                                        .align(Alignment.CenterVertically),
                                                    contentAlignment = Alignment.CenterStart
                                                ) {
                                                    Text(text = job.description ?: "Description")
                                                }

                                                // Cell 3
                                                Box(
                                                    modifier = Modifier
                                                        .weight(1f)
                                                        .fillMaxHeight()
                                                        .border(
                                                            BorderStroke(
                                                                0.5.dp,
                                                                color = colorResource(R.color.stroke_light)
                                                            ), // Border for all sides
                                                            shape = RectangleShape
                                                        )
                                                        .padding(
                                                            start = 8.dp,
                                                            top = 8.dp,
                                                            end = 8.dp
                                                        ), // Padding for the internal content
                                                    contentAlignment = Alignment.CenterStart
                                                ) {
                                                    Text(
                                                        text = "$${
                                                            String.format(
                                                                "%.1f",
                                                                job.sale_price?.toDouble() ?: 0.0
                                                            )
                                                        }" ?: "$0", fontWeight = FontWeight.Medium
                                                    )
                                                }

                                                // Cell 4 (Icon)
                                                Box(
                                                    modifier = Modifier
                                                        .width(48.dp) // fixed width for icon cell
                                                        .fillMaxHeight()
                                                        .border(
                                                            0.5.dp,
                                                            color = colorResource(R.color.stroke_light)
                                                        ),
                                                    contentAlignment = Alignment.Center
                                                ) {
                                                    IconButton(onClick = {
                                                        selectedJob = job
                                                        showJobDetailsSheet = true
                                                    }) {
                                                        Icon(
                                                            painter = painterResource(id = R.drawable.ic_arrow_up_right),
                                                            contentDescription = "Item Details",
                                                            tint = colorResource(R.color.profit_grey),
                                                            modifier = Modifier.size(16.dp)
                                                        )
                                                    }
                                                }
                                            }

                                        }
                                    }
                                }

                                Spacer(modifier = Modifier.height(12.dp))

//                        Totals
                                Card(
                                    modifier = Modifier.fillMaxWidth(),
                                    colors = CardDefaults.cardColors(Color.White),
                                    border = BorderStroke(1.dp, color = colorResource(R.color.stroke_soft)),
                                    shape = RoundedCornerShape(12.dp),
                                ) {
                                    Column(
                                        modifier = Modifier.padding(16.dp),
                                        verticalArrangement = Arrangement.spacedBy(8.dp)
                                    ) {
                                        Text(
                                            "Totals",
                                            fontWeight = FontWeight.Medium,
                                            color = colorResource(R.color.profit_black)
                                        )

                                        Row(
                                            modifier = Modifier.fillMaxWidth(),
                                            horizontalArrangement = Arrangement.SpaceBetween
                                        ) {

                                            Text(
                                                "Labor Budget",
                                                color = colorResource(R.color.text_sub)
                                            )
                                            Text(
                                                "$${
                                                    String.format(
                                                        "%.2f",
                                                        details.totals.labour_budget?.toDouble()
                                                    )
                                                }", fontWeight = FontWeight.Medium
                                            )

                                        }

                                        Row(
                                            modifier = Modifier.fillMaxWidth(),
                                            horizontalArrangement = Arrangement.SpaceBetween
                                        ) {

                                            Text(
                                                "Material Budget",
                                                color = colorResource(R.color.text_sub)
                                            )
                                            Text(
                                                "$${
                                                    String.format(
                                                        "%.2f",
                                                        details.totals.material_budget?.toDouble()
                                                    )
                                                }", fontWeight = FontWeight.Medium
                                            )


                                        }

                                        Row(
                                            modifier = Modifier.fillMaxWidth(),
                                            horizontalArrangement = Arrangement.SpaceBetween
                                        ) {

                                            Text(
                                                "Profit Overhead",
                                                color = colorResource(R.color.text_sub)
                                            )
                                            Text(
                                                "$${
                                                    String.format(
                                                        "%.2f",
                                                        details.totals.total_profit_overhead?.toDouble()
                                                    )
                                                }", fontWeight = FontWeight.Medium
                                            )


                                        }

                                        Row(
                                            modifier = Modifier.fillMaxWidth(),
                                            horizontalArrangement = Arrangement.SpaceBetween
                                        ) {

                                            Text(
                                                "Sale Price",
                                                color = colorResource(R.color.text_sub)
                                            )
                                            Text(
                                                "$${
                                                    String.format(
                                                        "%.2f",
                                                        details.totals.sale_price?.toDouble()
                                                    )
                                                }", fontWeight = FontWeight.Medium
                                            )


                                        }

//
                                    }
                                }

                                Spacer(modifier = Modifier.height(12.dp))

//                        Draws
                                if (!details.draws.isNullOrEmpty()) {
                                    Column {
                                        Text(
                                            "Draws",
                                            fontWeight = FontWeight.Medium,
                                            color = colorResource(R.color.profit_black)
                                        )
                                        Spacer(modifier = Modifier.height(8.dp))
                                        details.draws.forEach { draw ->
                                            DrawItem(draw = draw)
                                            Spacer(
                                                modifier = Modifier.height(12.dp).border(
                                                    0.5.dp,
                                                    color = colorResource(R.color.stroke_soft)
                                                )
                                            )
                                        }
                                    }
                                }
                                } // Close scrollable Column
                            }
//                            }
                        }

                    }

                    ResponsiveSheetContainer(
                        showSheet = showJobDetailsSheet && selectedJob != null,
                        onDismiss = {
                            showJobDetailsSheet = false
                            selectedJob = null
                        },
                        sheetState = sheetState,
                        headerContent = {
                            JobDetailsSheetHeader(
                                jobDetails = selectedJob,
                                onDismiss = {
                                    showJobDetailsSheet = false
                                    selectedJob = null
                                }
                            )
                        },
                        content = {
                            JobDetailsSheetContentBody(
                                jobDetails = selectedJob
                            )
                        }
                    )
                    ResponsiveSheetContainer(
                        showSheet = showBottomSheet,
                        onDismiss = { showBottomSheet = false },
                        sheetState = sheetState,
                        headerContent = {
                            ProjectDetailsBottomSheetHeader(
                                onDismiss = { showBottomSheet = false }
                            )
                        },
                        content = {
                            ProjectDetailsBottomSheetContentBody(
                                onOptionClick = { option ->
                                    showBottomSheet = false
                                    // Use the captured values
                                    val projectName = details.client_details.name ?: "Project Name"
                                    when (option) {
                                        "Change Order" -> onNavigateToLineItems(projectId, projectName)
                                        "View Estimate" -> {
                                            // Handle View Estimate action
                                        }
                                    }
                                }
                            )
                        }
                    )


                } ?: run {
                    Text("Project not found")
                }
            }

            Status.ERROR -> {
                dialog.hide()
                Text("Error: ${resource.message}")
            }
            else -> Text(text = "No Data Available")
        }
    }
}

@Composable
fun BottomSheetContent(onOptionClick: (String) -> Unit) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        TextButton(onClick = { onOptionClick("Change Order") }, ) {
            Text("Change Order", color = Color.Black, fontSize = 16.sp)
        }

        TextButton(onClick = { onOptionClick("View Estimate") },) {
            Text("View Estimate", color = Color.Black, fontSize = 16.sp)
        }
    }
}

@Composable
fun ProjectDetailsBottomSheetHeader(
    onDismiss: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        IconButton(onClick = onDismiss) {
            Icon(Icons.Filled.Close, contentDescription = "Close")
        }
        Text(
            text = "Project Options",
            fontSize = 18.sp,
            fontWeight = FontWeight.Medium,
            color = Color.Black
        )
        // Empty space to balance the layout
        Spacer(modifier = Modifier.width(48.dp))
    }
}

@Composable
fun ProjectDetailsBottomSheetContentBody(
    onOptionClick: (String) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        TextButton(onClick = { onOptionClick("Change Order") }) {
            Text("Change Order", color = Color.Black, fontSize = 16.sp)
        }

        TextButton(onClick = { onOptionClick("View Estimate") }) {
            Text("View Estimate", color = Color.Black, fontSize = 16.sp)
        }
    }
}

@Composable
fun DrawItem(draw: DrawsRespModel) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            ,
        border = BorderStroke(1.dp, color = colorResource(R.color.stroke_soft)),
        shape = RoundedCornerShape(12.dp),

        colors = CardDefaults.cardColors(Color.White)
    ) {
        Column(modifier = Modifier.padding(16.dp), verticalArrangement = Arrangement.spacedBy(8.dp)) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row {
                    Text("${draw.percentage}% " ?: "")
                    Spacer(modifier = Modifier.width(2.dp)) // Add horizontal space
                    Text("($${draw.amount})" ?: "")

                }
                Spacer(modifier = Modifier.height(2.dp))

                PaymentStatusBadge(draw.status ?: "")

//                Text(draw.payment_type ?: "", color = Color.Black, fontSize = 12.sp)
            }
            Text(draw.description ?: "description", fontWeight = FontWeight.Medium)
        }
    }
}

@Composable
fun PaymentStatusBadge(status: String) {
    val statusColor = when (status) {
        "1" -> colorResource(id = R.color.project_green_bg) // Replace with your actual color resource
        "2" -> colorResource(id = R.color.project_orange)
        else -> colorResource(id = R.color.project_yellow)
//        "Outstanding" -> colorResource(id = R.color.project_purple_bg)
//        else -> Color.Gray
    }

    val statusTextColor = when (status) {
        "1" -> colorResource(id = R.color.project_green) // Replace with your actual color resource
        "2" -> colorResource(id = R.color.project_brown)
        else -> colorResource(id = R.color.project_dark_yellow)
//        "Outstanding" -> colorResource(id = R.color.project_purple)
//        else -> Color.Gray
    }
    if(status.isEmpty()) return
//    val statusText = when (status) {
//        "0" -> "Credit card" // Replace with your actual color resource
//        "1" -> "Check"
//        "2" -> "Cash"
//        else -> ""
//    }
    val statusText = when (status) {
//        "0" -> "Recived" // Replace with your actual color resource
        "1" -> "Received"
        "2" -> "Outstanding"
        else -> "Pending"
    }

    Box(
        modifier = Modifier
            .background(statusColor, shape = RoundedCornerShape(8.dp))
            .padding(horizontal = 8.dp, vertical = 4.dp)
    ) {
        Text(text = statusText, color = statusTextColor, fontSize = 12.sp, fontWeight = FontWeight.Medium)
    }
}


@Composable
fun JobDetailsSheet(jobDetails: JobDetailsRespModel?, onDismiss: () -> Unit) {
    Column(modifier = Modifier.heightIn(min = 741.dp)) {

        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween

        ) {
            IconButton(onClick = onDismiss) {
                Icon(Icons.Default.Close, contentDescription = "Close")
            }
            Text(
                text = "${jobDetails?.description}" ?: "Job Details",
                style = MaterialTheme.typography.titleMedium,
                modifier = Modifier.weight(1f),
                textAlign = TextAlign.Center // Align the text to the center
            )
            IconButton(onClick = onDismiss) {
    // Icon(Icons.Default.Close, contentDescription = "Close")
            }
        }
        Spacer(modifier = Modifier.height(16.dp))


        Column() {
            // Materials table header
            Row(
                modifier = Modifier
                    .fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Box(
                    modifier = Modifier
                        .weight(2f)
                        .border(0.5.dp, color = colorResource(R.color.stroke_light))
                        .padding(8.dp),
                    contentAlignment = Alignment.CenterStart
                ) {
                    Text("", color = colorResource(R.color.profit_black)) // Empty header for material name
                }

                Box(
                    modifier = Modifier
                        .weight(1f)
                        .border(0.5.dp, color = colorResource(R.color.stroke_light))
                        .padding(8.dp),
                    contentAlignment = Alignment.CenterEnd
                ) {
                    Text("Cost", color = colorResource(R.color.profit_black))
                }

                Box(
                    modifier = Modifier
                        .weight(1f)
                        .border(0.5.dp, color = colorResource(R.color.stroke_light))
                        .padding(8.dp),
                    contentAlignment = Alignment.CenterEnd
                ) {
                    Text("Qty", color = colorResource(R.color.profit_black))
                }

                Box(
                    modifier = Modifier
                        .weight(1f)
                        .border(0.5.dp, color = colorResource(R.color.stroke_light))
                        .padding(8.dp),
                    contentAlignment = Alignment.CenterEnd
                ) {
                    Text("Total", color = colorResource(R.color.profit_black))
                }
            }

            // Materials data rows
            jobDetails?.materials?.forEach { material ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(IntrinsicSize.Min),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Material name cell
                    Box(
                        modifier = Modifier
                            .weight(2f)
                            .fillMaxHeight()
                            .border(0.5.dp, color = colorResource(R.color.stroke_light))
                            .padding(8.dp),
                        contentAlignment = Alignment.CenterStart
                    ) {
                        Text(
                            text = material.name ?: "(material name)",
                            color = colorResource(R.color.profit_black)
                        )
                    }

                    // Cost cell
                    Box(
                        modifier = Modifier
                            .weight(1f)
                            .fillMaxHeight()
                            .border(0.5.dp, color = colorResource(R.color.stroke_light))
                            .padding(8.dp),
                        contentAlignment = Alignment.CenterEnd
                    ) {
                        Text(
                            text = "$${String.format("%.2f", material.cost?.toDouble() ?: 0.0)}",
                            color = colorResource(R.color.profit_black),
                            fontWeight = FontWeight.Medium
                        )
                    }

                    // Quantity cell
                    Box(
                        modifier = Modifier
                            .weight(1f)
                            .fillMaxHeight()
                            .border(0.5.dp, color = colorResource(R.color.stroke_light))
                            .padding(8.dp),
                        contentAlignment = Alignment.CenterEnd
                    ) {
                        Text(
                            text = "${material.quantity ?: 0}",
                            color = colorResource(R.color.profit_black),
                            fontWeight = FontWeight.Medium
                        )
                    }

                    // Total cell
                    Box(
                        modifier = Modifier
                            .weight(1f)
                            .fillMaxHeight()
                            .border(0.5.dp, color = colorResource(R.color.stroke_light))
                            .padding(8.dp),
                        contentAlignment = Alignment.CenterEnd
                    ) {
                        Text(
                            text = "$${String.format("%.0f", material.total?.toDouble() ?: 0.0)}",
                            color = colorResource(R.color.profit_black),
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(Color.White)
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        "Labor hours",
                        fontWeight = FontWeight.Medium,
                        color = colorResource(R.color.profit_black)
                    )
                    Text(
                        "${jobDetails?.labour_hours ?: 0}",
                        fontWeight = FontWeight.Medium,
                        color = colorResource(R.color.profit_black)
                    )
                }

                Divider(color = colorResource(R.color.stroke_light), thickness = 1.dp)

                Spacer(modifier = Modifier.height(8.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text("Labor Budget", color = colorResource(R.color.text_sub))
                    Text(
                        "$${String.format("%.0f", jobDetails?.labour_budget?.toDouble() ?: 0.0)}",
                        fontWeight = FontWeight.Medium,
                        color = colorResource(R.color.profit_black)
                    )
                }

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text("Material Budget", color = colorResource(R.color.text_sub))
                    Text(
                        "$${String.format("%.0f", jobDetails?.material_budget?.toDouble() ?: 0.0)}",
                        fontWeight = FontWeight.Medium,
                        color = colorResource(R.color.profit_black)
                    )
                }

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text("Profit Overhead", color = colorResource(R.color.text_sub))
                    Text(
                        "$${String.format("%.0f", jobDetails?.profit_overhead_amount?.toDouble() ?: 0.0)}",
                        fontWeight = FontWeight.Medium,
                        color = colorResource(R.color.profit_black)
                    )
                }

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text("Sale Price", color = colorResource(R.color.text_sub))
                    Text(
                        "$${String.format("%.0f", jobDetails?.sale_price?.toDouble() ?: 0.0)}",
                        fontWeight = FontWeight.Medium,
                        color = colorResource(R.color.profit_black)
                    )
                }

//
            }
        }
    }
}

@Composable
fun JobDetailsSheetHeader(
    jobDetails: JobDetailsRespModel?,
    onDismiss: () -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        IconButton(onClick = onDismiss) {
            Icon(Icons.Default.Close, contentDescription = "Close")
        }
        Text(
            text = "${jobDetails?.description}" ?: "Job Details",
            style = MaterialTheme.typography.titleMedium,
            modifier = Modifier.weight(1f),
            textAlign = TextAlign.Center
        )
        IconButton(onClick = onDismiss) {
            // Empty space to balance the layout
        }
    }
}

@Composable
fun JobDetailsSheetContentBody(
    jobDetails: JobDetailsRespModel?
) {
    Column {
        Spacer(modifier = Modifier.height(16.dp))

        Column {
            // Materials table header
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Box(
                    modifier = Modifier
                        .weight(2f)
                        .border(0.5.dp, color = colorResource(R.color.stroke_light))
                        .padding(8.dp),
                    contentAlignment = Alignment.CenterStart
                ) {
                    Text("", color = colorResource(R.color.profit_black))
                }

                Box(
                    modifier = Modifier
                        .weight(1f)
                        .border(0.5.dp, color = colorResource(R.color.stroke_light))
                        .padding(8.dp),
                    contentAlignment = Alignment.CenterEnd
                ) {
                    Text("Cost", color = colorResource(R.color.profit_black))
                }

                Box(
                    modifier = Modifier
                        .weight(1f)
                        .border(0.5.dp, color = colorResource(R.color.stroke_light))
                        .padding(8.dp),
                    contentAlignment = Alignment.CenterEnd
                ) {
                    Text("Qty", color = colorResource(R.color.profit_black))
                }

                Box(
                    modifier = Modifier
                        .weight(1f)
                        .border(0.5.dp, color = colorResource(R.color.stroke_light))
                        .padding(8.dp),
                    contentAlignment = Alignment.CenterEnd
                ) {
                    Text("Total", color = colorResource(R.color.profit_black))
                }
            }

            // Materials data rows
            jobDetails?.materials?.forEach { material ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(IntrinsicSize.Min),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Material name cell
                    Box(
                        modifier = Modifier
                            .weight(2f)
                            .fillMaxHeight()
                            .border(0.5.dp, color = colorResource(R.color.stroke_light))
                            .padding(8.dp),
                        contentAlignment = Alignment.CenterStart
                    ) {
                        Text(
                            text = material.name ?: "",
                            color = colorResource(R.color.profit_black)
                        )
                    }

                    // Cost cell
                    Box(
                        modifier = Modifier
                            .weight(1f)
                            .fillMaxHeight()
                            .border(0.5.dp, color = colorResource(R.color.stroke_light))
                            .padding(8.dp),
                        contentAlignment = Alignment.CenterEnd
                    ) {
                        Text(
                            text = "$${String.format("%.2f", material.cost?.toDouble() ?: 0.0)}",
                            color = colorResource(R.color.profit_black)
                        )
                    }

                    // Quantity cell
                    Box(
                        modifier = Modifier
                            .weight(1f)
                            .fillMaxHeight()
                            .border(0.5.dp, color = colorResource(R.color.stroke_light))
                            .padding(8.dp),
                        contentAlignment = Alignment.CenterEnd
                    ) {
                        Text(
                            text = "${material.quantity ?: 0}",
                            color = colorResource(R.color.profit_black)
                        )
                    }

                    // Total cell
                    Box(
                        modifier = Modifier
                            .weight(1f)
                            .fillMaxHeight()
                            .border(0.5.dp, color = colorResource(R.color.stroke_light))
                            .padding(8.dp),
                        contentAlignment = Alignment.CenterEnd
                    ) {
                        val total = (material.cost?.toDouble() ?: 0.0) * (material.quantity?.toDouble() ?: 0.0)
                        Text(
                            text = "$${String.format("%.2f", total)}",
                            color = colorResource(R.color.profit_black)
                        )
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(containerColor = colorResource(R.color.white))
        ) {
            Column(modifier = Modifier.padding(16.dp)) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text("Labor Hours", color = colorResource(R.color.text_sub))
                    Text(
                        "${jobDetails?.labour_hours ?: 0}",
                        fontWeight = FontWeight.Medium,
                        color = colorResource(R.color.profit_black)
                    )
                }

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text("Labor Budget", color = colorResource(R.color.text_sub))
                    Text(
                        "$${String.format("%.0f", jobDetails?.labour_budget?.toDouble() ?: 0.0)}",
                        fontWeight = FontWeight.Medium,
                        color = colorResource(R.color.profit_black)
                    )
                }

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text("Material Budget", color = colorResource(R.color.text_sub))
                    Text(
                        "$${String.format("%.0f", jobDetails?.material_budget?.toDouble() ?: 0.0)}",
                        fontWeight = FontWeight.Medium,
                        color = colorResource(R.color.profit_black)
                    )
                }

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text("Profit Overhead", color = colorResource(R.color.text_sub))
                    Text(
                        "$${String.format("%.0f", jobDetails?.profit_overhead_amount?.toDouble() ?: 0.0)}",
                        fontWeight = FontWeight.Medium,
                        color = colorResource(R.color.profit_black)
                    )
                }

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text("Sale Price", color = colorResource(R.color.text_sub))
                    Text(
                        "$${String.format("%.0f", jobDetails?.sale_price?.toDouble() ?: 0.0)}",
                        fontWeight = FontWeight.Medium,
                        color = colorResource(R.color.profit_black)
                    )
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun ProjectDetailsScreenPreview() {
//    ProjectDetailsScreen(1, BaasViewModel())
}

@Preview(showBackground = true)
@Composable
fun JobDetailsSheetPreview() {
//    JobDetailsSheet(null)
}

@Preview(showBackground = true)
@Composable
fun DrawItemPreview() {
    DrawItem(DrawsRespModel(
        1, 1, "", "", "", "", 606, "1", "", "3",
        remainingPercentage = "10"
    ))
}