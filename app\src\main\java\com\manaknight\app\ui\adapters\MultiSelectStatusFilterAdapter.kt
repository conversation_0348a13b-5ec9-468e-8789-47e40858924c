package com.manaknight.app.ui.adapters

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import Manaknight.R

class MultiSelectStatusFilterAdapter(
    private val statusOptions: List<String>,
    private var selectedStatuses: Set<String>,
    private val onStatusSelectionChanged: (String, Boolean) -> Unit
) : RecyclerView.Adapter<MultiSelectStatusFilterAdapter.StatusViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): StatusViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_multi_select_filter_option, parent, false)
        return StatusViewHolder(view)
    }

    override fun onBindViewHolder(holder: StatusViewHolder, position: Int) {
        val status = statusOptions[position]
        holder.bind(status, selectedStatuses.contains(status))
    }

    override fun getItemCount(): Int = statusOptions.size

    fun updateSelectedStatuses(newSelectedStatuses: Set<String>) {
        selectedStatuses = newSelectedStatuses
        notifyDataSetChanged()
    }

    inner class StatusViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val textView: TextView = itemView.findViewById(R.id.tvFilterOption)
        private val checkBox: CheckBox = itemView.findViewById(R.id.cbFilterOption)

        fun bind(status: String, isSelected: Boolean) {
            textView.text = status
            checkBox.isChecked = isSelected

            // Set checkbox color to brand green
            checkBox.buttonTintList = itemView.context.getColorStateList(R.color.brand_green)

            // Clear any existing listeners to avoid conflicts
            itemView.setOnClickListener(null)
            checkBox.setOnClickListener(null)

            val clickListener = View.OnClickListener {
                val currentState = selectedStatuses.contains(status)
                val newCheckedState = !currentState
                onStatusSelectionChanged(status, newCheckedState)
            }

            itemView.setOnClickListener(clickListener)
        }
    }
}
