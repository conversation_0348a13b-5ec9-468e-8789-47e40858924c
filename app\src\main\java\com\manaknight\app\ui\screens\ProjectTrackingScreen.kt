package com.manaknight.app.ui.screens

import Manaknight.R
import android.app.Dialog
import android.os.Bundle
import android.util.Log
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.ui.unit.sp
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.MoreVert
import com.manaknight.app.network.Status
import com.manaknight.app.viewmodels.*
import com.manaknight.app.viewmodels.BaasViewModel
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.navigation.NavController
import com.manaknight.app.model.remote.TrackingLabourResponse
import com.manaknight.app.model.remote.TrackingMaterialResponse
import com.manaknight.app.model.remote.profitPro.DrawItem
import com.manaknight.app.model.remote.profitPro.Draws
import com.manaknight.app.model.remote.profitPro.Labor
import com.manaknight.app.model.remote.profitPro.MaterialItem
import com.manaknight.app.model.remote.profitPro.Materials
import com.manaknight.app.model.remote.profitPro.ProjectTrackingResponse
import com.manaknight.app.network.Resource
import com.manaknight.app.ui.components.CustomCheckbox
import com.manaknight.app.utils.CustomUtils.formatDate
import androidx.compose.ui.window.Dialog
import androidx.navigation.fragment.findNavController
import com.manaknight.app.extensions.snackBarForDialog
import com.manaknight.app.ui.utils.isTabletLayout
import com.manaknight.app.ui.utils.getMaxContentWidth
import com.manaknight.app.ui.components.ResponsiveSheetContainer


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProjectTrackingScreen(projectId: Int, projectName:String, baasViewModel: BaasViewModel,  dialog: Dialog, navController: NavController, onSaveMaterial: (String, String) -> Unit, onEditMaterial: (String, String, Int) -> Unit, onUpdateDraws: (Int, String, DrawItem) -> Unit) {
    LaunchedEffect(projectId) {
        baasViewModel.setTrackingProjectId(projectId)
        baasViewModel.fetchProjectTrackingData(projectId) // Ensure data is fetched when projectId changes
    }

    val trackingResponseResource by baasViewModel.trackingResponse.observeAsState(initial = Resource.loading(null))
    var showStatusFilter by remember { mutableStateOf(false) }
    var selectedStatus by remember { mutableStateOf("All") }
    val sheetState = rememberModalBottomSheetState()
    LaunchedEffect(trackingResponseResource?.status) {
        when (trackingResponseResource?.status) {
            Status.LOADING -> dialog.show()
            Status.SUCCESS, Status.ERROR -> dialog.hide()
            else -> {}
        }
    }

    var selectedTab by remember { mutableStateOf("Draws") }
    var showAddMaterialSheet by remember { mutableStateOf(false) }
    var showEditLaborSheet by remember { mutableStateOf(false) }
    var showAddPaymentMethodSheet by remember { mutableStateOf(false) }
    var showInvoiceOptionsSheet by remember { mutableStateOf(false) }
    var selectedDrawItem by remember { mutableStateOf<DrawItem?>(null) }
    var filterStatus by remember { mutableStateOf<String?>(null) } // To handle the filter


    // Access the ProjectTrackingResponse data when in the SUCCESS state
    val projectTrackingResponse = trackingResponseResource?.data


    Surface(color = Color.White) {
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            // Top Bar - Fixed at top (always full width)
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(56.dp), // typical toolbar height
                contentAlignment = Alignment.Center
            ) {
                // Back Button
                IconButton(
                    onClick = { navController.popBackStack() },
                    modifier = Modifier.align(Alignment.CenterStart)
                ) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "Back")
                }

                // Title Text - always centered
                Text(
                    text = "Tracking",
                    style = MaterialTheme.typography.headlineSmall,
                    fontSize = 16.sp
                )

                // More Options Button
                IconButton(
                    onClick = { /* Handle more options */ },
                    modifier = Modifier.align(Alignment.CenterEnd)
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_options_bold),
                        contentDescription = "More",
                        tint = colorResource(R.color.black),
                        modifier = Modifier.size(16.dp)
                    )
                }
            }

            // Scrollable content area - responsive adaptation
            Box(
                modifier = Modifier.fillMaxSize()
            ) {
                Column(
                    modifier = Modifier
                        .then(
                            if (isTabletLayout()) {
                                Modifier
                                    .align(Alignment.TopCenter)
                                    .widthIn(max = getMaxContentWidth())
                            } else {
                                Modifier.fillMaxWidth()
                            }
                        )
                        .verticalScroll(rememberScrollState())
                        .padding(16.dp)
                ) {
                Spacer(modifier = Modifier.height(16.dp))

                // Project Info (You might want to observe project details here as well)
                Text(
                    text = "#$projectId • ${projectName}", // Replace with actual project name if available
                    style = MaterialTheme.typography.titleMedium
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Tabs
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    TabButton(text = "Draws", selected = selectedTab == "Draws") {
                        selectedTab = "Draws"
                    }
                    TabButton(text = "Labor", selected = selectedTab == "Labor") {
                        selectedTab = "Labor"
                    }
                    TabButton(text = "Materials", selected = selectedTab == "Materials") {
                        selectedTab = "Materials"
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

            // Observe the project tracking response
            val trackingResponseResource by baasViewModel.trackingResponse.observeAsState(
                initial = Resource.loading(
                    null
                )
            )

            // Display content based on the state of the tracking response
            when (trackingResponseResource?.status) {
                Status.LOADING -> {
                    // Show loading indicator
                    dialog.show()
                }

                Status.SUCCESS -> {
                    dialog.hide()
                    trackingResponseResource.data?.let { response ->
                        // Check for errors in the response itself (if your API returns an error flag)
                        if (response.error == true) {
                            // Handle the error case based on the response's error flag
                            Text(text = "Error: ${response.error}") // Replace with proper error UI
                        } else if (response.trackingData != null) {
                            // Access the tracking data
                            val trackingData = response.trackingData
                            // Content based on selected tab
                            when (selectedTab) {
                                "Draws" -> DrawsContent(
                                    drawData = trackingData.draws,
                                    selectedStatus = selectedStatus,
                                    showStatusFilter = { showStatusFilter = it },
                                    onShowPaymentMethodSheet = { drawItem ->
                                        selectedDrawItem = drawItem
                                        showAddPaymentMethodSheet = true
                                    },
                                    onShowInvoiceOptionsSheet = { drawItem ->
                                        selectedDrawItem = drawItem
                                        showInvoiceOptionsSheet = true
                                    })

                                "Labor" -> trackingData.labor?.let {
                                    LaborContent(
                                        laborData = it,
                                        onLaborEditClick = { showEditLaborSheet = true })
                                } // Directly use laborData
                                "Materials" -> trackingData.materials?.let {
                                    MaterialsContent(
                                        materialsData = it,
                                        onNewMaterialClick = { showAddMaterialSheet = true },
                                        onEditMaterial = { materialName: String, unitCost: String, id: Int ->
                                            onEditMaterial(
                                                materialName,
                                                unitCost,
                                                id
                                            )
                                        })
                                } // Directly use materialsData
                                else -> DrawsContent(
                                    drawData = trackingData.draws,
                                    selectedStatus = selectedStatus,
                                    showStatusFilter = { showStatusFilter = it },
                                    onShowPaymentMethodSheet = { drawItem ->
                                        selectedDrawItem = drawItem
                                        showAddPaymentMethodSheet = true
                                    },
                                    onShowInvoiceOptionsSheet = { drawItem ->
                                        selectedDrawItem = drawItem
                                        showInvoiceOptionsSheet = true
                                    })
                            }
                        } else {
                            // Handle the case where data is null but status is SUCCESS (unexpected)
                            Text(text = "No tracking data available.")
                        }
                    }
                }

                Status.ERROR -> {
                    dialog.hide()
                    // Show error message
                    Text(text = "Error fetching tracking data: ${trackingResponseResource.message}")
                }

                else -> {
                    // Optional: Handle other states if needed
                    Text(text = "Idle State")
                }
            }

                // Add some bottom padding to ensure content doesn't get cut off
                Spacer(modifier = Modifier.height(100.dp))
                }
            }

        // Bottom sheets - outside of scrollable content
        if (showAddMaterialSheet) {
            var materialName by remember { mutableStateOf("") }
            var unitCost by remember { mutableStateOf("") }

            ResponsiveSheetContainer(
                showSheet = showAddMaterialSheet,
                onDismiss = { showAddMaterialSheet = false },
                sheetState = sheetState,
                headerContent = {
                    AddMaterialExpenseSheetHeader(
                        materialName = materialName,
                        unitCost = unitCost,
                        onDismiss = { showAddMaterialSheet = false },
                        onSave = { name, cost ->
                            onSaveMaterial(name, cost)
                            showAddMaterialSheet = false
                            println("Saving material: $name, unit cost: $cost")
                        }
                    )
                },
                content = {
                    AddMaterialExpenseSheetContentBody(
                        materialName = materialName,
                        onMaterialNameChange = { materialName = it },
                        unitCost = unitCost,
                        onUnitCostChange = { unitCost = it }
                    )
                }
            )
        }
            ResponsiveSheetContainer(
                showSheet = showEditLaborSheet,
                onDismiss = { showEditLaborSheet = false },
                sheetState = sheetState,
                headerContent = {
                    EditLaborSheetHeader(
                        onDismiss = { showEditLaborSheet = false }
                    )
                },
                content = {
                    EditLaborSheetContentBody()
                }
            )
            if (showStatusFilter) {
                ModalBottomSheet(
                    onDismissRequest = { showStatusFilter = false },
                    sheetState = sheetState,
                    containerColor = Color.White
                ) {
                    DrawStatusFilterSheet(
                        onDismiss = { showStatusFilter = false },
                        onStatusSelected = { status ->
                            selectedStatus = status
                            showStatusFilter = false
                        }
                    )
                }
            }
            if (showAddPaymentMethodSheet && selectedDrawItem != null) {
                ModalBottomSheet(
                    onDismissRequest = {
                        showAddPaymentMethodSheet = false
                        selectedDrawItem = null
                    },
                    sheetState = sheetState,
                    containerColor = Color.White
                ) {
                    AddPaymentMethodSheet(
                        drawItem = selectedDrawItem!!,
                        onDismiss = {
                            showAddPaymentMethodSheet = false
                            selectedDrawItem = null
                        },
                        onSave = { paymentMethodId, checkNumber ->
                            // Call the callback to handle updateDraws in the Fragment
                            selectedDrawItem?.let { drawItem ->
                                onUpdateDraws(paymentMethodId, checkNumber, drawItem)
                                showAddPaymentMethodSheet = false
                                selectedDrawItem = null
                            }
                        }
                    )
                }
            }

            // Invoice Options Sheet
            if (showInvoiceOptionsSheet && selectedDrawItem != null) {
                ModalBottomSheet(
                    onDismissRequest = {
                        showInvoiceOptionsSheet = false
                        selectedDrawItem = null
                    },
                    sheetState = sheetState,
                    containerColor = Color.White
                ) {
                    InvoiceOptionsSheet(
                        drawItem = selectedDrawItem!!,
                        onDismiss = {
                            showInvoiceOptionsSheet = false
                            selectedDrawItem = null
                        },
                        onViewInvoice = {
                            // Handle View Invoice action - Navigate to Invoice screen
                            showInvoiceOptionsSheet = false
                            val projectIdToNavigate = selectedDrawItem?.projectId ?: projectId
                            selectedDrawItem = null

                            // Navigate to Invoice screen with project ID
                            val bundle = Bundle().apply {
                                putInt("projectID", projectIdToNavigate)
                            }
                            navController.navigate(R.id.InvoiceFragment, bundle)
                        },
                        onSendInvoice = {
                            // Handle Send Invoice action
                            showInvoiceOptionsSheet = false
                            selectedDrawItem = null
                            // TODO: Implement send invoice logic
                        }
                    )
                }
            }
        }
    }



}



@Composable
fun TabButton(text: String, selected: Boolean, onClick: () -> Unit) {
    Button(
        onClick = onClick,
        shape = RoundedCornerShape(4.dp),
        colors = ButtonDefaults.buttonColors(
            containerColor  = if (selected) colorResource(R.color.white) else Color.Transparent
        ),
        border = BorderStroke(1.dp, if (selected) colorResource(R.color.black) else Color.Transparent),
        contentPadding = PaddingValues(horizontal = 16.dp, vertical = 2.dp), // Tight padding
        modifier = Modifier
            .padding(2.dp)
            .height(28.dp)
            .wrapContentWidth() // Shrinks button to fit content
            .defaultMinSize(minWidth = 0.dp)

    ) {
        Text(text = text, color = if (selected) Color.Black else Color.LightGray)
    }
}

@Composable
fun DrawsContent(
    drawData: Draws?,
    selectedStatus: String = "All",
    showStatusFilter: (Boolean) -> Unit,
    onShowPaymentMethodSheet: (DrawItem) -> Unit,
    onShowInvoiceOptionsSheet: (DrawItem) -> Unit
) {

    DrawsStatusFilterDropdown(selectedStatus) {
        showStatusFilter(true)
    }

    Spacer(modifier = Modifier.height(8.dp))

    when  {
        drawData == null -> {
            Text("No draws data available.")
        }
       else -> {
           val drawsList = drawData.list
           if (drawsList.isNullOrEmpty()) {
               Text("No draws available.")
           }

            if (drawsList != null) {
               Column {
                    drawsList.forEachIndexed { index, drawItem ->
                        ListItem(
                            number = index + 1,
                            amount = drawItem.amount?.let { try { "$${String.format("%.2f", it.toDouble())}" } catch (e: NumberFormatException) { "$$it" } } ?: "N/A",
                            badge = getDrawStatusText(drawItem.paymentType?.toString()),
                            status = drawItem.status?.toString(),
                            date = drawItem.updatedAt?.toString(),
                            onMoreOptionsClick = { onShowInvoiceOptionsSheet(drawItem) },
                            onCheckmarkClick = { onShowPaymentMethodSheet(drawItem) }
                        )
                    }
                }
            } else {
                Text("No draws data available.")
            }
        }


    }
}

// Status Filter Sheet Composable (Custom Sheet Implementation)
@Composable
fun DrawStatusFilterSheet(onDismiss: () -> Unit, onStatusSelected: (String) -> Unit) {

            Column(modifier = Modifier.padding(16.dp)) {
//                Text("Select Status", fontWeight = FontWeight.Bold, fontSize = 18.sp)
                Spacer(Modifier.height(8.dp))
                StatusOption("All", onStatusSelected)
                StatusOption("Additional charge", onStatusSelected)
                StatusOption("Credits", onStatusSelected)
                StatusOption("Adjusted by credit", onStatusSelected)

            }

}

// Status Filter Dropdown Composable
@Composable
fun DrawsStatusFilterDropdown(selectedStatus: String, onClick: () -> Unit) {


    Button(
        onClick = onClick,
        shape = RoundedCornerShape(4.dp),
        colors = ButtonDefaults.buttonColors(containerColor = colorResource(R.color.white)),
        border = BorderStroke(1.dp, colorResource(R.color.stroke_soft)),
        contentPadding = PaddingValues(horizontal = 8.dp, vertical = 2.dp), // Tight padding
        modifier = Modifier
            .padding(2.dp)
            .height(28.dp)
            .wrapContentWidth() // Shrinks button to fit content
            .defaultMinSize(minWidth = 0.dp) // Prevents enforcing min width
    ) {
        Text(
            text =  " $selectedStatus",
            fontSize = 16.sp,
            color = colorResource(R.color.text_sub)
        )
        Spacer(modifier = Modifier.width(4.dp)) // Space between text and icon
        Icon(
            painter = painterResource(id = R.drawable.ic_dropdown), // Replace with your actual icon
            tint = colorResource(R.color.text_sub),
            contentDescription = "Filter Status",
            modifier = Modifier.size(20.dp)
        )
    }



}

// Dropdown Filter Button Composable
@Composable
fun DrawsDropdownFilter(label: String) {
    Button(
        onClick = { /* Handle dropdown */ },
        colors = ButtonDefaults.buttonColors(colorResource(R.color.white)),
        shape = RoundedCornerShape(8.dp)
    ) {
        Text(text = label, color = Color.Black)
        Icon(
            painter = painterResource(id = R.drawable.ic_dropdown), // Replace with your actual icon
            contentDescription = "Add Project",
            modifier = Modifier.size(16.dp)
        )
    }
}

@Composable
fun LaborContent(laborData: Labor, onLaborEditClick: () -> Unit) {
    Column {
        when  {
            (laborData == null )  -> {
                Text("No draws data available.")
            }
           else -> {



                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .border(0.5.dp, Color.LightGray, RoundedCornerShape(8.dp))
                        .padding(top = 12.dp, bottom = 12.dp),
                    verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    LaborSummaryItem(label = "Labor budget",percentageValue = "", value = "$${laborData?.laborBudget?.let { String.format("%.2f", it) } ?: "N/A"}")
                    Divider(thickness = 1.dp, color = Color.LightGray)
                    LaborSummaryItem(label = "Labor spent", percentageValue = "(${laborData?.laborSpentPercentage?.let { try { String.format("%.0f", it.toDouble()) } catch (e: NumberFormatException) { it } } ?: "0"}%) ",  value = "$${laborData?.laborSpent?.let { String.format("%.2f", it) } ?: "N/A"}") // Adjust as needed
                    Divider(thickness = 1.dp, color = Color.LightGray)
                    LaborSummaryItem(label = "Remaining budget", percentageValue = "(${laborData?.remainingPercentage?.let { try { String.format("%.0f", it.toDouble()) } catch (e: NumberFormatException) { it } } ?: "0"}%) ",  value = "$${laborData?.laborBalance?.let { String.format("%.2f", it) } ?: "N/A"}") // Adjust as needed
                }

                Spacer(modifier = Modifier.height(16.dp))

               if (laborData == null || laborData.teamMembers.isNullOrEmpty()){
                   Text(
                       text = "No labor costs available.",
                       style = MaterialTheme.typography.titleMedium
                   )

               }
               else{
                   Text(
                       text = "Labor costs",
                       style = MaterialTheme.typography.titleMedium
                   )

                   Spacer(modifier = Modifier.height(8.dp))


                   val laborList = laborData?.teamMembers
                   Column(
                       modifier = Modifier
                           .fillMaxWidth()
                           .border(0.5.dp, Color.LightGray, RoundedCornerShape(8.dp))
                           .padding(12.dp)
                   ) {
                       if (laborList != null) {
                           laborList.forEach { it ->
                               // You'll likely need more details per labor cost item.
                               // Consider updating your backend response or creating a more detailed model.
                               LaborCostItem(name = it.name?:"", rate = it.cost?:"", hours = it.hours?:"", total = (it.total?:"").toString(), onLaborEditClick)
                           }
                       }
                   }

               }


            }


        }
    }
}

@Composable
fun LaborSummaryItem(label: String, value: String,percentageValue:String) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp, horizontal = 12.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(text = label, fontSize = 14.sp)
        Text(text = percentageValue,  fontSize = 14.sp, color = colorResource(R.color.text_sub))
        Text(text = value, fontWeight = FontWeight.Medium, fontSize = 14.sp, color = colorResource(R.color.profit_black))
    }
}

@Composable
fun LaborCostItem(name: String, rate: String, hours: String, total: String, onLaborEditClick: () -> Unit) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {



        // Cell 1
        Box(
            modifier = Modifier
                .weight(1f)
                .fillMaxHeight()
                .border(
                    0.5.dp,
                    color = colorResource(R.color.stroke_light)
                )
                .padding(8.dp)
                .align(Alignment.CenterVertically),
            contentAlignment = Alignment.CenterStart
        ) {

            Column() {
                Text(text = name, color = colorResource(R.color.profit_black))
                Text(text = rate, color = colorResource(R.color.text_sub), style = MaterialTheme.typography.labelSmall)
            }

        }

        // Cell 2
        Box(
            modifier = Modifier
                .weight(2f)
                .fillMaxHeight()
                .border(
                    0.5.dp,
                    color = colorResource(R.color.stroke_light)
                )
                .padding(8.dp)
                .align(Alignment.CenterVertically),
            contentAlignment = Alignment.CenterStart
        ) {
            Text(text = hours, modifier = Modifier.width(40.dp),color = colorResource(R.color.profit_black), fontWeight = FontWeight.Medium)

        }
        // Cell 3
        Box(
            modifier = Modifier
                .weight(2f)
                .fillMaxHeight()
                .border(
                    0.5.dp,
                    color = colorResource(R.color.stroke_light)
                )
                .padding(8.dp)
                .align(Alignment.CenterVertically),
            contentAlignment = Alignment.CenterStart
        ) {
            Text(text = total, modifier = Modifier.width(60.dp),color = colorResource(R.color.profit_black), fontWeight = FontWeight.Medium)
              }


        // Cell 4 (Icon)
        Box(
            modifier = Modifier
                .width(48.dp) // fixed width for icon cell
                .fillMaxHeight()
                .border(
                    0.5.dp,
                    color = colorResource(R.color.stroke_light)
                ),
            contentAlignment = Alignment.Center
        ) {


            IconButton(onClick = { onLaborEditClick() }) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_edit_icon), // Replace with your actual icon
                    tint = colorResource(R.color.text_sub),
                    contentDescription = "Edit Labor Cost",
                    modifier = Modifier.size(20.dp)
                )
            }
        }








    }
}

@Composable
fun ListItem(
    number: Int,
    amount: String,
    badge: String?,
    status: String? = null,
    date: String? = null,
    onMoreOptionsClick: () -> Unit = {},
    onCheckmarkClick: () -> Unit = {}
) {
    // Check if status is 1 (checked state)
    val isChecked = status?.toIntOrNull() == 1

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
            .border(0.5.dp, Color.LightGray, RoundedCornerShape(8.dp)),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Spacer(modifier = Modifier.height(16.dp))
        CustomCheckbox(
            checked = isChecked,
            onCheckedChange = { onCheckmarkClick() }
        )

//        Box(
//            modifier = Modifier
//                .size(30.dp) // Size of the checkbox box
//                .clip(RoundedCornerShape(6.dp)) // Rounded corners
//                .background(Color.LightGray), // Optional background color
//            contentAlignment = Alignment.Center
//        ) {
//            Checkbox(
//                checked = false,
//                onCheckedChange = {  },
//                modifier = Modifier
//                    .size(24.dp) // Inner checkbox size
//            )
//        }
        Text(text = "$number. $amount", modifier = Modifier.weight(1f))
        Log.d("StatusDebug", "Status value: $badge")
        if (badge != null) {

            TrackingStatusBadge(status = badge, date = date)
        }

        IconButton(onClick = { onMoreOptionsClick() }) {

            Icon(
                painter = painterResource(id = R.drawable.ic_options_bold),
                contentDescription = "More",
                tint = colorResource(R.color.gray),
                modifier = Modifier.size(16.dp)
            )
        }
    }
}

// Function to map draw status codes to readable text
fun getDrawStatusText(status: String?): String? {

    return when (status) {
        "0" -> "Credit card"
        "1" -> "Check"
        "2" -> "Cash"
        "4" -> "Completed"
        "5" -> "Pending"
        "6" -> "Approved"
        "7" -> "Rejected"
        "8" -> "In Progress"
        "9" -> "On Hold"
        "10" -> "Cancelled"
        else -> status ?: null
    }
}

@Composable
fun TrackingStatusBadge(status: String, date: String?) {
    val statusColor = when (status) {
        "Credit card" -> colorResource(id = R.color.project_blue_bg)
        "Check" -> colorResource(id = R.color.project_green_bg)
        "Cash" -> colorResource(id = R.color.project_purple_bg)
        "Outstanding" -> colorResource(id = R.color.project_purple_bg)
        "Pending" -> colorResource(id = R.color.project_blue_bg)
        "Approved" -> colorResource(id = R.color.project_green_bg)
        "Rejected" -> Color(0xFFFFEBEE) // Light red background
        "In Progress" -> colorResource(id = R.color.project_blue_bg)
        "On Hold" -> Color(0xFFFFF3E0) // Light orange background
        "Cancelled" -> Color(0xFFFFEBEE) // Light red background
        "Adjusted by credit" -> colorResource(id = R.color.project_blue_bg)
        "Credit" -> colorResource(id = R.color.project_purple_bg)
        "Additional charge" -> colorResource(id = R.color.project_purple_bg)
        else -> Color.Transparent
    }

    val statusTextColor = when (status) {
        "Credit card" -> colorResource(id = R.color.project_blue)
        "Check" -> colorResource(id = R.color.project_green)
        "Cash" -> colorResource(id = R.color.project_purple)
        "Outstanding" -> colorResource(id = R.color.project_purple)
        "Pending" -> colorResource(id = R.color.project_blue)
        "Approved" -> colorResource(id = R.color.project_green)
        "Rejected" -> Color(0xFFD32F2F) // Red text
        "In Progress" -> colorResource(id = R.color.project_blue)
        "On Hold" -> Color(0xFFFF8F00) // Orange text
        "Cancelled" -> Color(0xFFD32F2F) // Red text
        "Adjusted by credit" -> colorResource(id = R.color.project_blue)
        "Credit" -> colorResource(id = R.color.project_purple)
        "Additional charge" -> colorResource(id = R.color.project_purple)
        else -> Color.Gray
    }

    Box(
        modifier = Modifier
            .background(statusColor, shape = RoundedCornerShape(8.dp))
            .padding(horizontal = 8.dp, vertical = 4.dp)
    ) {
        Row(horizontalArrangement = Arrangement.spacedBy(2.dp)){
            if (status.isNotEmpty()) {
                Text(text = status, color = statusTextColor, fontSize = 12.sp)
            }
            if (date != null) {
                Text(text = formatDate(date) , color = statusTextColor, fontSize = 12.sp)
            }

        }

    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MaterialsContent(
    materialsData: Materials,
    onNewMaterialClick: () -> Unit,
    onEditMaterial: (String, String, Int) -> Unit,
) {
    var showEditSheet by remember { mutableStateOf(false) }
    var selectedItem by remember { mutableStateOf<MaterialItem?>(null) }
    val sheetState = rememberModalBottomSheetState()
    Column {
        when  {

            materialsData == null -> {
                Text("No Materials data available.")
            }
            else -> {

                val materials = materialsData

                if (materials != null) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .border(0.5.dp, Color.LightGray, RoundedCornerShape(8.dp))
                            .padding(vertical = 12.dp),
                        verticalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        LaborSummaryItem(
                            label = "Material budget",
                            percentageValue = "",
                            value = "$${materials?.materialBudget?.let { String.format("%.2f", it) } ?: "N/A"}"
                        )
                        Divider(thickness = 1.dp, color = colorResource(R.color.stroke_soft))
                        LaborSummaryItem(
                            label = "Material spent",
                            percentageValue = "",
                            value = "$${materials?.materialSpent?.let { String.format("%.2f", it) } ?: "N/A"}"
                        )
                        Divider(thickness = 1.dp, color = colorResource(R.color.stroke_soft))
                        LaborSummaryItem(
                            label = "Material balance",
                            percentageValue = "",
                            value = "$${materials?.materialBalance?.let { String.format("%.2f", it) } ?: "N/A"}"
                        )
                    }
                }
                else {
                    Text("No Materials data available.")
                }

                Spacer(modifier = Modifier.height(16.dp))
                Column(modifier = Modifier
                    .fillMaxWidth()
                    .border(0.5.dp, Color.LightGray, RoundedCornerShape(8.dp))
                    .padding(vertical=12.dp)) {

                    Row(
                        modifier = Modifier.fillMaxWidth().padding(horizontal=12.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Materials",
                            style = MaterialTheme.typography.titleMedium
                        )


                        Button(
                            onClick = { onNewMaterialClick() },
                            shape = RoundedCornerShape(4.dp),
                            colors = ButtonDefaults.buttonColors(containerColor = colorResource(R.color.white)),
                            border = BorderStroke(1.dp, colorResource(R.color.profit_blue)),
                            contentPadding = PaddingValues(
                                horizontal = 8.dp,
                                vertical = 2.dp
                            ), // Tight padding
                            modifier = Modifier
                                .padding(2.dp)
                                .height(28.dp)
                                .wrapContentWidth() // Shrinks button to fit content
                                .defaultMinSize(minWidth = 0.dp) // Prevents enforcing min width
                        ) {
                            Text(
                                text = "New",
                                fontSize = 16.sp,
                                color = colorResource(R.color.profit_blue)
                            )
                            Spacer(modifier = Modifier.width(4.dp)) // Space between text and icon
                            Icon(
                                painter = painterResource(id = R.drawable.ic_add),
                                contentDescription = "New Material",
                                tint = colorResource(R.color.profit_blue),
                                modifier = Modifier.size(16.dp)
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    // List of materials

                    val materialsList = materials?.materials ?: emptyList()

                    // Use LazyColumn for efficient scrolling of potentially many items
                    LazyColumn(
                        modifier = Modifier
                            .fillMaxWidth()
                            .border(0.5.dp, Color.LightGray, RoundedCornerShape(8.dp))
                            .padding(vertical = 12.dp)
                            .heightIn(max = 400.dp) // Optional: Give it a max height if you want it to scroll independently within the section
                        // Remove verticalScroll here if you use LazyColumn, as LazyColumn is inherently scrollable
                    ) {
                        items(materialsList) { materialItem ->
                            // Your MaterialItem Composable
                            // Example: MaterialItem(materialItem, onEditMaterial)
                            MaterialItem(
                                materialItem = materialItem,
                                onEditClick = {
                                    selectedItem = materialItem
                                    showEditSheet = true
                                }
                            )
                              }
                    }

                    // Use Column instead of LazyColumn for better scrolling behavior
//                    materialsList.forEach { materialItem ->
//                        MaterialItem(
//                            materialItem = materialItem,
//                            onEditClick = {
//                                selectedItem = materialItem
//                                showEditSheet = true
//                            }
//                        )
//                    }

                }
                Spacer(modifier = Modifier.height(16.dp))

                if (showEditSheet && selectedItem != null) {
                    var materialName by remember { mutableStateOf(selectedItem?.name ?: "") }
                    var unitCost by remember { mutableStateOf(selectedItem?.cost?.toString() ?: "") }

                    ResponsiveSheetContainer(
                        showSheet = showEditSheet,
                        onDismiss = {
                            showEditSheet = false
                            selectedItem = null
                        },
                        sheetState = sheetState,
                        headerContent = {
                            EditMaterialExpenseSheetHeader(
                                materialName = materialName,
                                unitCost = unitCost,
                                materialId = selectedItem?.id ?: 0,
                                onDismiss = {
                                    showEditSheet = false
                                    selectedItem = null
                                },
                                onSave = { name, cost, id ->
                                    onEditMaterial(name, cost, id)
                                    println("Saving updated material: $name, unit cost: $cost, id: $id")
                                    showEditSheet = false
                                    selectedItem = null
                                }
                            )
                        },
                        content = {
                            EditMaterialExpenseSheetContentBody(
                                materialName = materialName,
                                onMaterialNameChange = { materialName = it },
                                unitCost = unitCost,
                                onUnitCostChange = { unitCost = it }
                            )
                        }
                    )
                }


            }

        }
    }
}

@Composable
fun MaterialItem(materialItem: MaterialItem, onEditClick: (MaterialItem) -> Unit) {
    val formattedDate = formatDate(materialItem.createdAt)
   val  date = formattedDate ?: "mm/dd/yyyy"
    val amount = materialItem?.cost ?: "N/A"

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .height(IntrinsicSize.Min),
        verticalAlignment = Alignment.CenterVertically
    ) {


            // Cell 1
            Box(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxHeight()
                    .border(
                        0.5.dp,
                        color = colorResource(R.color.stroke_light)
                    )
                   .padding(12.dp)
                    .align(Alignment.CenterVertically),
                contentAlignment = Alignment.CenterStart
            ) {
                Text(
                    text = "${date}" ?: "",
                    color = colorResource(R.color.profit_black)
                )
            }

            // Cell 2
            Box(
                modifier = Modifier
                    .weight(2f)
                    .fillMaxHeight()
                    .border(
                        0.5.dp,
                        color = colorResource(R.color.stroke_light)
                    )
                   .padding(8.dp)
                    .align(Alignment.CenterVertically),
                contentAlignment = Alignment.CenterStart
            ) {
                Column {
                    // Text(
                    //     text = materialItem.name ?: "Material Name",
                    //     color = colorResource(R.color.profit_black),
                    //     fontWeight = FontWeight.Medium
                    // )
                    Text(
                        text = "$${amount}",
                        color = colorResource(R.color.profit_black),
                        style = MaterialTheme.typography.labelSmall,
                        fontWeight = FontWeight.Medium
                    )
                }
            }


            // Cell 3 (Icon)
            Box(
                modifier = Modifier
                    .width(48.dp) // fixed width for icon cell
                    .fillMaxHeight()
                    .border(
                        0.5.dp,
                        color = colorResource(R.color.stroke_light)
                    ),
                contentAlignment = Alignment.Center
            ) {
                IconButton(onClick = { onEditClick(materialItem) }) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_edit_icon), // Replace with your actual icon
                        tint = colorResource(R.color.text_sub),
                        contentDescription = "Edit Material",
                        modifier = Modifier.size(20.dp)
                    )
                }
            }












    }

}

@Composable
fun AddMaterialExpenseSheet(onDismiss: () -> Unit, onSave: (String, String) -> Unit) {
    var materialName by remember { mutableStateOf("") } // State to hold the material name
    var unitCost by remember { mutableStateOf("") } // State to hold the input value
    Column(modifier = Modifier.padding(16.dp).heightIn(min = 741.dp)) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(56.dp), // Adjust height as needed
            contentAlignment = Alignment.Center
        ) {
            // Close button aligned to start
            IconButton(
                onClick = onDismiss,
                modifier = Modifier.align(Alignment.CenterStart)
            ) {
                Icon(Icons.Default.Close, contentDescription = "Close")
            }

            // Title always centered
            Text(
                text = "Add Material Expense",
                style = MaterialTheme.typography.headlineSmall
            )

            // Save button aligned to end
            Button(
                onClick = {
                    if (materialName.isNotBlank() && unitCost.isNotBlank()) {
                        onSave(materialName, unitCost)
                    }
                },
                shape = RoundedCornerShape(4.dp),
                colors = ButtonDefaults.buttonColors(containerColor = colorResource(R.color.white)),
                border = BorderStroke(1.dp, colorResource(R.color.profit_blue)),
                contentPadding = PaddingValues(
                    horizontal = 8.dp,
                    vertical = 2.dp
                ),
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .padding(2.dp)
                    .height(28.dp)
                    .wrapContentWidth()
                    .defaultMinSize(minWidth = 0.dp)
            ) {
                Text(
                    text = "Save",
                    fontSize = 16.sp,
                    color = colorResource(R.color.profit_blue)
                )
            }
        }


        Spacer(modifier = Modifier.height(16.dp))

        Text(text = "Material name")
        OutlinedTextField(
            value = materialName, // Connect the value to the state
            onValueChange = { newValue -> materialName = newValue }, // Update the state on value change
            modifier = Modifier.fillMaxWidth(),
            label = { Text("Enter material name") }, // Optional label
            shape = RoundedCornerShape(16.dp)
        )

        Spacer(modifier = Modifier.height(16.dp))

        Text(text = "Unit cost")
        Spacer(modifier = Modifier.height(4.dp))
        OutlinedTextField(
            value = unitCost,
            onValueChange = { newValue -> unitCost = newValue },
            modifier = Modifier.fillMaxWidth(),
            leadingIcon = {
                Text(
                    text = "$",
                    style = MaterialTheme.typography.bodyLarge,
                    color = colorResource(R.color.text_sub)
                )
            },
            placeholder = { Text("0.00") },
            shape = RoundedCornerShape(16.dp)
        )
    }
}


@Composable
fun EditMaterialExpenseSheet(
    onDismiss: () -> Unit,
    materialItem: MaterialItem,
    onSave: (String, String, Int) -> Unit
) {
    var materialName by remember { mutableStateOf(materialItem.name ?: "") }
    var unitCost by remember { mutableStateOf(materialItem.cost.toString()) }


            Column(modifier = Modifier.padding(16.dp).heightIn(min = 741.dp)) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(56.dp), // Adjust height if needed
                    contentAlignment = Alignment.Center
                ) {
                    // Close button aligned to start
                    IconButton(
                        onClick = onDismiss,
                        modifier = Modifier.align(Alignment.CenterStart)
                    ) {
                        Icon(Icons.Default.Close, contentDescription = "Close")
                    }

                    // Title always centered
                    Text(
                        text = "Edit Material Expense",
                        style = MaterialTheme.typography.headlineSmall
                    )

                    // Save button aligned to end
                    Button(
                        onClick = {
                            if (materialName.isNotBlank() && unitCost.isNotBlank()) {
                                onSave(materialName, unitCost, materialItem.id ?: 0)
                            }
                        },
                        shape = RoundedCornerShape(4.dp),
                        colors = ButtonDefaults.buttonColors(containerColor = colorResource(R.color.white)),
                        border = BorderStroke(1.dp, colorResource(R.color.profit_blue)),
                        contentPadding = PaddingValues(
                            horizontal = 8.dp,
                            vertical = 2.dp
                        ),
                        modifier = Modifier
                            .align(Alignment.CenterEnd)
                            .padding(2.dp)
                            .height(28.dp)
                            .wrapContentWidth()
                            .defaultMinSize(minWidth = 0.dp)
                    ) {
                        Text(
                            text = "Save",
                            fontSize = 16.sp,
                            color = colorResource(R.color.profit_blue)
                        )
                    }
                }


                Spacer(modifier = Modifier.height(16.dp))

                Text(text = "Material name")
                Spacer(modifier = Modifier.height(4.dp))
                OutlinedTextField(
                    value = materialName,
                    onValueChange = { materialName = it },
                    modifier = Modifier.fillMaxWidth().padding(0.dp),
                    label = { Text("Enter material name") },
                    shape = RoundedCornerShape(16.dp),
                )

                Spacer(modifier = Modifier.height(16.dp))

                Text(text = "Unit cost")
                Spacer(modifier = Modifier.height(4.dp))
                OutlinedTextField(
                    value = unitCost,
                    onValueChange = { unitCost = it },
                    modifier = Modifier.fillMaxWidth().padding(0.dp),
                    leadingIcon = {
                        Text(
                            text = "$",
                            style = MaterialTheme.typography.bodyLarge,
                            color = colorResource(R.color.text_sub)
                        )
                    },
                    placeholder = { Text("0.00") },
                    shape = RoundedCornerShape(16.dp),
                )

                Spacer(modifier = Modifier.height(16.dp))

//                Text(text = "Date: ${materialItem.createdAt}")
//                Text(text = "Amount: ${materialItem.cost}")

                 }

}

@Composable
fun EditLaborSheet(onDismiss: () -> Unit) {

        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onDismiss) {
                Icon(Icons.Default.Close, contentDescription = "Close")
            }
            Text(
                text = "Edit {employee.name} Hours",
                style = MaterialTheme.typography.headlineSmall,
                modifier = Modifier.weight(1f)
            )
            TextButton(onClick = { /* Handle save */ }) {
                Text("Save")
            }
        }

        Spacer(modifier = Modifier.height(16.dp))
        Text(text =  "{employee.name}") // Assuming 'model' might be related to employee
        Spacer(modifier = Modifier.height(8.dp))
        Text(text = "Hours")
        Spacer(modifier = Modifier.height(8.dp))
        OutlinedTextField(
            value = "25", // You'll need to fetch and update this value
            onValueChange = { /* Handle hours change */ },
            modifier = Modifier
                .fillMaxWidth(),
            shape = RoundedCornerShape(16.dp)

        )

}

@Composable
fun InvoiceOptionsSheet(
    drawItem: DrawItem,
    onDismiss: () -> Unit,
    onViewInvoice: () -> Unit,
    onSendInvoice: () -> Unit
) {
    Column(modifier = Modifier.padding(16.dp).heightIn(min = 300.dp)) {
        // Header
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(56.dp),
            contentAlignment = Alignment.Center
        ) {
            // Close button aligned to start
            IconButton(
                onClick = onDismiss,
                modifier = Modifier.align(Alignment.CenterStart)
            ) {
                Icon(Icons.Default.Close, contentDescription = "Close")
            }

            // Title always centered
            Text(
                text = "Invoice Options",
                style = MaterialTheme.typography.headlineSmall
            )
        }

        Spacer(modifier = Modifier.height(24.dp))

        // View Invoice option
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { onViewInvoice() }
                .padding(vertical = 16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {



            Text(
                text = "View Invoice",
                fontSize = 16.sp,
                color = Color.Black
            )
        }

        // Send Invoice option
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { onSendInvoice() }
                .padding(vertical = 16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {



            Text(
                text = "Send Invoice",
                fontSize = 16.sp,
                color = Color.Black
            )
        }
    }
}

@Composable
fun AddPaymentMethodSheet(drawItem: DrawItem, onDismiss: () -> Unit, onSave: (Int, String) -> Unit) {
    // Initialize selectedPaymentMethod based on existing paymentType from DrawItem
    // Use -1 to represent "no selection" state for new payment method selection
    val initialPaymentMethod = when (drawItem.paymentType) {
        "0" -> 0 // Credit card
        "1" -> 1 // Check
        "2" -> 2 // Cash
        else -> -1 // No selection - user must choose a payment method
    }

    var selectedPaymentMethod by remember { mutableStateOf(initialPaymentMethod) }
    var checkNumber by remember { mutableStateOf(drawItem.checkNo ?: "") }

    // Determine if Save button should be enabled
    val isSaveEnabled = when (selectedPaymentMethod) {
        -1 -> false // No payment method selected
        1 -> checkNumber.isNotBlank() // Check selected - require check number
        0, 2 -> true // Credit card or Cash selected
        else -> false
    }

    // Payment method mapping: 0 = Credit card, 1 = Check, 2 = Cash
    val paymentMethodLabels = mapOf(
        0 to "Credit card",
        1 to "Check",
        2 to "Cash"
    )

    Column(modifier = Modifier.padding(16.dp).heightIn(min = 741.dp)) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(56.dp),
            contentAlignment = Alignment.Center
        ) {
            // Close button aligned to start
            IconButton(
                onClick = onDismiss,
                modifier = Modifier.align(Alignment.CenterStart)
            ) {
                Icon(Icons.Default.Close, contentDescription = "Close")
            }

            // Title always centered
            Text(
                text = "Add Payment Method",
                style = MaterialTheme.typography.headlineSmall
            )

            // Save button aligned to end
            Button(
                onClick = {
                    onSave(selectedPaymentMethod, checkNumber)
                },
                enabled = isSaveEnabled,
                shape = RoundedCornerShape(4.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = if (isSaveEnabled) colorResource(R.color.white) else Color.LightGray,
                    disabledContainerColor = Color.LightGray
                ),
                border = BorderStroke(
                    1.dp,
                    if (isSaveEnabled) colorResource(R.color.profit_blue) else Color.Gray
                ),
                contentPadding = PaddingValues(
                    horizontal = 8.dp,
                    vertical = 2.dp
                ),
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .padding(2.dp)
                    .height(28.dp)
                    .wrapContentWidth()
                    .defaultMinSize(minWidth = 0.dp)
            ) {
                Text(
                    text = "Save",
                    fontSize = 16.sp,
                    color = if (isSaveEnabled) colorResource(R.color.profit_blue) else Color.Gray
                )
            }
        }

        Spacer(modifier = Modifier.height(24.dp))

        // Credit card option
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { selectedPaymentMethod = 0 }
                .padding(vertical = 12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            RadioButton(
                selected = selectedPaymentMethod == 0,
                onClick = { selectedPaymentMethod = 0 },
                colors = RadioButtonDefaults.colors(
                    selectedColor = colorResource(R.color.brand_green)
                )
            )

            Spacer(modifier = Modifier.width(12.dp))

            Text(
                text = "Credit card",
                fontSize = 16.sp,
                color = Color.Black
            )
        }

        // Check option
        Column(){
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { selectedPaymentMethod = 1 }
                    .padding(vertical = 12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = selectedPaymentMethod == 1,
                    onClick = { selectedPaymentMethod = 1 },
                    colors = RadioButtonDefaults.colors(
                        selectedColor = colorResource(R.color.brand_green)
                    )
                )

                Spacer(modifier = Modifier.width(12.dp))

                Text(
                    text = "Check",
                    fontSize = 16.sp,
                    color = Color.Black
                )
            }

            // Check number field - positioned under the check option
            if (selectedPaymentMethod == 1) {
                Spacer(modifier = Modifier.height(16.dp))
                Column( Modifier.padding(start = 32.dp)) {
                    Text(
                        text = "* Check number",
                        fontSize = 14.sp,
                        color = Color.Black
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    OutlinedTextField(
                        value = checkNumber,
                        onValueChange = { checkNumber = it },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(40.dp), // User prefers 40dp height for input fields
                        shape = RoundedCornerShape(8.dp),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = Color(0xFF007AFF),
                            unfocusedBorderColor = Color.LightGray
                        )
                    )

                }


            }

        }


        // Cash option
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { selectedPaymentMethod = 2 }
                .padding(vertical = 12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            RadioButton(
                selected = selectedPaymentMethod == 2,
                onClick = { selectedPaymentMethod = 2 },
                colors = RadioButtonDefaults.colors(
                    selectedColor = colorResource(R.color.brand_green)
                )
            )

            Spacer(modifier = Modifier.width(12.dp))

            Text(
                text = "Cash",
                fontSize = 16.sp,
                color = Color.Black
            )
        }


    }
}




//@Preview(showBackground = true)
//@Composable
//fun TrackingScreenPreview() {
//    MaterialTheme {
//        // Provide dummy BaasViewModel and Dialog for preview
//        ProjectTrackingScreen(
//            projectId = 123,
//            baasViewModel = BaasViewModel(FakeBaasRepository()),
//            dialog = Dialog(android.content.ContextWrapper(null)),
//            navController = androidx.navigation.compose.rememberNavController()
//        )
//    }
//}
//
//// Dummy Repository for Preview
//class FakeBaasRepository : com.manaknight.app.repositories.APIRepository {
//    override suspend fun getTrackingDraws(projectId: Int): TrackingDrawsResponse {
//        return TrackingDrawsResponse(error = false, message = null, list = "Item 1, Item 2", mapping = null, amount = "$100", status = "Paid", name = "Draw 1")
//    }
//
//    override suspend fun getTrackingMaterials(projectId: Int): TrackingMaterialResponse {
//        return TrackingMaterialResponse(error = false, message = null, model = "Material Model", material_budget = "$500", material_spent = "$200", material_balance = "$300", materials = "$50, $150")
//    }
//
//    override suspend fun getTrackingLabor(projectId: Int): TrackingLabourResponse {
//        return TrackingLabourResponse(error = false, message = null, list = "John Doe, Jane Smith", labor_budget = "$1000", labor_spent_amount = "$400", labor_spent_percentage = "40%", remaining_budget_amount = "$600", remaining_budget_percentage = "60%")
//    }
//
//    // Implement other repository functions if needed for other parts of your app
//}

// Header and Content components for ResponsiveSheetContainer

@Composable
fun AddMaterialExpenseSheetHeader(
    materialName: String,
    unitCost: String,
    onDismiss: () -> Unit,
    onSave: (String, String) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        IconButton(onClick = onDismiss) {
            Icon(Icons.Default.Close, contentDescription = "Close")
        }
        Text(
            text = "Add Material Expense",
            style = MaterialTheme.typography.headlineSmall
        )
        TextButton(
            onClick = { onSave(materialName, unitCost) }
        ) {
            Text("Save")
        }
    }
}

@Composable
fun AddMaterialExpenseSheetContentBody(
    materialName: String,
    onMaterialNameChange: (String) -> Unit,
    unitCost: String,
    onUnitCostChange: (String) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        Text(text = "Material name")
        Spacer(modifier = Modifier.height(4.dp))
        OutlinedTextField(
            value = materialName,
            onValueChange = onMaterialNameChange,
            modifier = Modifier.fillMaxWidth(),
            placeholder = { Text("Enter material name") },
            shape = RoundedCornerShape(16.dp)
        )

        Spacer(modifier = Modifier.height(16.dp))

        Text(text = "Unit cost")
        Spacer(modifier = Modifier.height(4.dp))
        OutlinedTextField(
            value = unitCost,
            onValueChange = onUnitCostChange,
            modifier = Modifier.fillMaxWidth(),
            leadingIcon = {
                Text(
                    text = "$",
                    style = MaterialTheme.typography.bodyLarge,
                    color = colorResource(R.color.text_sub)
                )
            },
            placeholder = { Text("0.00") },
            shape = RoundedCornerShape(16.dp)
        )
    }
}

@Composable
fun EditMaterialExpenseSheetHeader(
    materialName: String,
    unitCost: String,
    materialId: Int,
    onDismiss: () -> Unit,
    onSave: (String, String, Int) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        IconButton(onClick = onDismiss) {
            Icon(Icons.Default.Close, contentDescription = "Close")
        }
        Text(
            text = "Edit Material Expense",
            style = MaterialTheme.typography.headlineSmall
        )
        TextButton(
            onClick = { onSave(materialName, unitCost, materialId) }
        ) {
            Text("Save")
        }
    }
}

@Composable
fun EditMaterialExpenseSheetContentBody(
    materialName: String,
    onMaterialNameChange: (String) -> Unit,
    unitCost: String,
    onUnitCostChange: (String) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        Text(text = "Material name")
        Spacer(modifier = Modifier.height(4.dp))
        OutlinedTextField(
            value = materialName,
            onValueChange = onMaterialNameChange,
            modifier = Modifier.fillMaxWidth(),
            placeholder = { Text("Enter material name") },
            shape = RoundedCornerShape(16.dp)
        )

        Spacer(modifier = Modifier.height(16.dp))

        Text(text = "Unit cost")
        Spacer(modifier = Modifier.height(4.dp))
        OutlinedTextField(
            value = unitCost,
            onValueChange = onUnitCostChange,
            modifier = Modifier.fillMaxWidth(),
            leadingIcon = {
                Text(
                    text = "$",
                    style = MaterialTheme.typography.bodyLarge,
                    color = colorResource(R.color.text_sub)
                )
            },
            placeholder = { Text("0.00") },
            shape = RoundedCornerShape(16.dp)
        )
    }
}

@Composable
fun EditLaborSheetHeader(
    onDismiss: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        IconButton(onClick = onDismiss) {
            Icon(Icons.Default.Close, contentDescription = "Close")
        }
        Text(
            text = "Edit {employee.name} Hours",
            style = MaterialTheme.typography.headlineSmall,
            modifier = Modifier.weight(1f)
        )
        TextButton(onClick = { /* Handle save */ }) {
            Text("Save")
        }
    }
}

@Composable
fun EditLaborSheetContentBody() {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        Text("Labor editing content goes here")
        // Add your labor editing UI components here
    }
}




























