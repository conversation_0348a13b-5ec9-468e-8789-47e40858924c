<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:background="@color/app_bg_color">

        <!-- Header Layout -->
        <include
            android:id="@+id/headerInclude"
            layout="@layout/header" />

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- Type and Month Filters -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/statusFilterButton"
                            android:layout_width="wrap_content"
                            android:layout_height="32dp"
                            android:text="Status: All"
                            android:textSize="14sp"
                            android:fontFamily="@font/inter"
                            android:textColor="@color/black"
                            android:background="@drawable/filter_button_background"
                            android:paddingHorizontal="12dp"
                            android:paddingVertical="4dp"
                            android:drawableEnd="@drawable/ic_dropdown"
                            android:drawablePadding="4dp"
                            android:drawableTint="@color/black"
                            android:gravity="center_vertical"
                            android:clickable="true"
                            android:focusable="true"
                            android:foreground="?android:attr/selectableItemBackground" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:gravity="end">

                        <TextView
                            android:id="@+id/monthFilterButton"
                            android:layout_width="wrap_content"
                            android:layout_height="32dp"
                            android:text="All"
                            android:textSize="14sp"
                            android:fontFamily="@font/inter"
                            android:textColor="@color/black"
                            android:background="@drawable/filter_button_background"
                            android:paddingHorizontal="12dp"
                            android:paddingVertical="4dp"
                            android:drawableEnd="@drawable/ic_dropdown"
                            android:drawablePadding="4dp"
                            android:drawableTint="@color/black"
                            android:gravity="center_vertical"
                            android:clickable="true"
                            android:focusable="true"
                            android:foreground="?android:attr/selectableItemBackground" />

                    </LinearLayout>

                </LinearLayout>

                <!-- Profit & Health Cards -->
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_centerHorizontal="true"
                        android:padding="16dp">

                        <!-- Total Profit Card -->
                        <androidx.cardview.widget.CardView
                            android:layout_width="170dp"
                            android:layout_height="120dp"
                            android:layout_weight="1"
                            app:cardCornerRadius="24dp"
                            app:cardElevation="4dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:padding="10dp"
                                android:background="@color/profit_black"
                                android:orientation="vertical">
                                <ImageView
                                    android:layout_width="@dimen/_20sdp"
                                    android:layout_height="@dimen/_13sdp"
                                    android:src="@drawable/profit_overhead"/>

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Total profit overhead"
                                    android:textSize="14sp"
                                    android:layout_marginTop="8dp"
                                    android:fontFamily="@font/inter"
                                    android:textFontWeight="400"
                                    android:textColor="#ffffff"
                                    android:alpha="0.8"/>

                                <TextView
                                    android:id="@+id/txtDashboardTotalProfitOverhead"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="$0"
                                    android:textSize="24sp"
                                    android:layout_marginTop="8dp"
                                    android:fontFamily="@font/inter"
                                    android:textFontWeight="600"
                                    android:textColor="#ffffff"
                                    android:singleLine="true"
                                    android:ellipsize="end"
                                    app:autoSizeTextType="uniform"
                                    app:autoSizeMinTextSize="10sp"
                                    app:autoSizeMaxTextSize="24sp"
                                    app:autoSizeStepGranularity="1sp" />

                            </LinearLayout>

                        </androidx.cardview.widget.CardView>

                        <View android:layout_width="16dp" android:layout_height="match_parent"/>

                        <!-- Company Health Card -->
                        <androidx.cardview.widget.CardView
                            android:layout_width="170dp"
                            android:layout_height="120dp"
                            android:layout_weight="1"
                            app:cardCornerRadius="24dp"
                            app:cardElevation="4dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:padding="10dp"
                                android:background="@color/project_green"
                                android:orientation="vertical">
                                <ImageView
                                    android:layout_width="@dimen/_20sdp"
                                    android:layout_height="@dimen/_13sdp"
                                    android:src="@drawable/compney_health"/>



                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Company health"
                                    android:textSize="14sp"
                                    android:layout_marginTop="8dp"
                                    android:fontFamily="@font/inter"
                                    android:textFontWeight="400"
                                    android:textColor="#ffffff"
                                    android:alpha="0.8"/>

                                <HorizontalScrollView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:scrollbars="none">
                                    <TextView
                                        android:id="@+id/txtDashboardCompanyHealth"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="0%"
                                        android:textSize="24sp"
                                        android:layout_marginTop="8dp"
                                        android:fontFamily="@font/inter"
                                        android:textFontWeight="600"
                                        android:textColor="#60e73e"
                                        android:singleLine="true"
                                        android:ellipsize="none" />
                                </HorizontalScrollView>

                            </LinearLayout>

                        </androidx.cardview.widget.CardView>

                    </LinearLayout>
                </RelativeLayout>

                <!-- Statistics Grid -->
                <GridLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:columnCount="2"
                    android:rowCount="3"
                    android:layout_marginTop="16dp"
                    android:layout_marginLeft="16dp"
                    android:layout_marginRight="16dp"
                    android:background="@drawable/bg_stat_card"
                    android:padding="16dp">

                    <!-- Row 1 -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_columnWeight="1"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="8dp">

                        <TextView
                            android:id="@+id/txtDashboardTotalContracts"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textSize="16sp"
                            android:fontFamily="@font/inter"
                            android:textFontWeight="700"
                            android:textColor="@color/profit_black"
                            android:singleLine="true"
                            android:textAlignment="center"
                            app:autoSizeTextType="uniform"
                            app:autoSizeMinTextSize="10sp"
                            app:autoSizeMaxTextSize="16sp"
                            app:autoSizeStepGranularity="1sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Total contracts"
                            android:textSize="12sp"
                            android:layout_marginTop="2dp"
                            android:fontFamily="@font/inter"
                            android:textFontWeight="400"
                            android:textColor="@color/profit_grey" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_columnWeight="1"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="8dp">

                        <TextView
                            android:id="@+id/txtDashboardTotalAR"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textSize="16sp"
                            android:fontFamily="@font/inter"
                            android:textFontWeight="700"
                            android:textColor="@color/profit_black"
                            android:singleLine="true"
                            android:textAlignment="center"
                            app:autoSizeTextType="uniform"
                            app:autoSizeMinTextSize="10sp"
                            app:autoSizeMaxTextSize="16sp"
                            app:autoSizeStepGranularity="1sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Total AR"
                            android:textSize="12sp"
                            android:layout_marginTop="2dp"
                            android:fontFamily="@font/inter"
                            android:textFontWeight="400"
                            android:textColor="@color/profit_grey" />

                    </LinearLayout>

                    <!-- Row 2 -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_columnWeight="1"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="8dp">

                        <TextView
                            android:id="@+id/txtDashboardMaterialBalance"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="$0"
                            android:textSize="16sp"
                            android:fontFamily="@font/inter"
                            android:textFontWeight="700"
                            android:textColor="@color/profit_black"
                            android:singleLine="true"
                            android:textAlignment="center"
                            app:autoSizeTextType="uniform"
                            app:autoSizeMinTextSize="10sp"
                            app:autoSizeMaxTextSize="16sp"
                            app:autoSizeStepGranularity="1sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Material balance"
                            android:textSize="12sp"
                            android:layout_marginTop="2dp"
                            android:fontFamily="@font/inter"
                            android:textFontWeight="400"
                            android:textColor="@color/profit_grey" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_columnWeight="1"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="8dp">

                        <TextView
                            android:id="@+id/txtDashboardMaterialSpent"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="$0"
                            android:textSize="16sp"
                            android:fontFamily="@font/inter"
                            android:textFontWeight="700"
                            android:textColor="@color/profit_black"
                            android:singleLine="true"
                            android:textAlignment="center"
                            app:autoSizeTextType="uniform"
                            app:autoSizeMinTextSize="10sp"
                            app:autoSizeMaxTextSize="16sp"
                            app:autoSizeStepGranularity="1sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Material spent"
                            android:textSize="12sp"
                            android:layout_marginTop="2dp"
                            android:fontFamily="@font/inter"
                            android:textFontWeight="400"
                            android:textColor="@color/profit_grey" />

                    </LinearLayout>

                    <!-- Row 3 -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_columnWeight="1"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="8dp">

                        <TextView
                            android:id="@+id/txtDashboardLaborBalance"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textSize="16sp"
                            android:fontFamily="@font/inter"
                            android:textFontWeight="700"
                            android:textAlignment="center"
                            android:textColor="@color/profit_black"
                            android:singleLine="true"
                            app:autoSizeTextType="uniform"
                            app:autoSizeMinTextSize="10sp"
                            app:autoSizeMaxTextSize="16sp"
                            app:autoSizeStepGranularity="1sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Labor balance"
                            android:textSize="12sp"
                            android:layout_marginTop="2dp"
                            android:fontFamily="@font/inter"
                            android:textFontWeight="400"
                            android:textColor="@color/profit_grey" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_columnWeight="1"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="8dp">

                        <TextView
                            android:id="@+id/txtDashboardLaborSpent"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textSize="16sp"
                            android:fontFamily="@font/inter"
                            android:textFontWeight="700"
                            android:textAlignment="center"
                            android:textColor="@color/profit_black"
                            android:singleLine="true"
                            app:autoSizeTextType="uniform"
                            app:autoSizeMinTextSize="10sp"
                            app:autoSizeMaxTextSize="16sp"
                            app:autoSizeStepGranularity="1sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Labor spent"
                            android:textSize="12sp"
                            android:layout_marginTop="2dp"
                            android:fontFamily="@font/inter"
                            android:textFontWeight="400"
                            android:textColor="@color/profit_grey" />

                    </LinearLayout>

                </GridLayout>

                <!-- Projects Section -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginTop="24dp"
                    android:layout_marginHorizontal="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Projects"
                        android:textSize="18sp"
                        android:fontFamily="@font/inter"
                        android:textFontWeight="600"
                        android:textColor="@color/profit_black"
                        android:layout_marginBottom="16dp" />

                    <!-- Projects RecyclerView -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recyclerViewProjects"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="true"
                        android:overScrollMode="never" />

                </LinearLayout>

            </LinearLayout>

        </ScrollView>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
