<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_dashboardview" modulePackage="Manaknight" filePath="app\src\main\res\layout\fragment_dashboardview.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_dashboardview_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="481" endOffset="51"/></Target><Target tag="binding_1" view="LinearLayout"><Expressions/><location startLine="7" startOffset="4" endLine="479" endOffset="18"/></Target><Target id="@+id/headerInclude" tag="binding_1" include="header"><Expressions/><location startLine="14" startOffset="8" endLine="16" endOffset="37"/></Target><Target id="@+id/statusFilterButton" view="TextView"><Expressions/><location startLine="41" startOffset="24" endLine="58" endOffset="89"/></Target><Target id="@+id/monthFilterButton" view="TextView"><Expressions/><location startLine="69" startOffset="24" endLine="86" endOffset="89"/></Target><Target id="@+id/txtDashboardTotalProfitOverhead" view="TextView"><Expressions/><location startLine="134" startOffset="32" endLine="149" endOffset="71"/></Target><Target id="@+id/txtDashboardCompanyHealth" view="TextView"><Expressions/><location startLine="193" startOffset="36" endLine="204" endOffset="66"/></Target><Target id="@+id/txtDashboardTotalContracts" view="TextView"><Expressions/><location startLine="235" startOffset="24" endLine="249" endOffset="63"/></Target><Target id="@+id/txtDashboardTotalAR" view="TextView"><Expressions/><location startLine="271" startOffset="24" endLine="285" endOffset="63"/></Target><Target id="@+id/txtDashboardMaterialBalance" view="TextView"><Expressions/><location startLine="308" startOffset="24" endLine="322" endOffset="63"/></Target><Target id="@+id/txtDashboardMaterialSpent" view="TextView"><Expressions/><location startLine="344" startOffset="24" endLine="358" endOffset="63"/></Target><Target id="@+id/txtDashboardLaborBalance" view="TextView"><Expressions/><location startLine="381" startOffset="24" endLine="395" endOffset="63"/></Target><Target id="@+id/txtDashboardLaborSpent" view="TextView"><Expressions/><location startLine="417" startOffset="24" endLine="431" endOffset="63"/></Target><Target id="@+id/recyclerViewProjects" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="466" startOffset="20" endLine="471" endOffset="56"/></Target></Targets></Layout>