package com.manaknight.app.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000r\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0006\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\b\n\u0002\b\f\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000f\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\u001at\u0010\u0000\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u000328\u0010\u0004\u001a4\u0012\u0015\u0012\u0013\u0018\u00010\u0006\u00a2\u0006\f\b\u0007\u0012\b\b\b\u0012\u0004\b\b(\t\u0012\u0013\u0012\u00110\n\u00a2\u0006\f\b\u0007\u0012\b\b\b\u0012\u0004\b\b(\u000b\u0012\u0004\u0012\u00020\u00010\u00052\u0006\u0010\f\u001a\u00020\n2\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\n0\u000e2\f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\n0\u000eH\u0007\u001a$\u0010\u0010\u001a\u00020\u00012\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\n0\u000e2\f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\n0\u000eH\u0007\u001at\u0010\u0011\u001a\u00020\u00012\u0006\u0010\f\u001a\u00020\n2\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\n0\u000e2\f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\n0\u000e2\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u000328\u0010\u0004\u001a4\u0012\u0015\u0012\u0013\u0018\u00010\u0006\u00a2\u0006\f\b\u0007\u0012\b\b\b\u0012\u0004\b\b(\t\u0012\u0013\u0012\u00110\n\u00a2\u0006\f\b\u0007\u0012\b\b\b\u0012\u0004\b\b(\u000b\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001a\u00b2\u0001\u0010\u0012\u001a\u00020\u00012\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\n2\u0006\u0010\u0016\u001a\u00020\n2\b\u0010\u0017\u001a\u0004\u0018\u00010\n2\u0006\u0010\u0018\u001a\u00020\n2\b\u0010\u0019\u001a\u0004\u0018\u00010\n2\u0006\u0010\u001a\u001a\u00020\n2\b\u0010\u001b\u001a\u0004\u0018\u00010\n2\u0006\u0010\u001c\u001a\u00020\n2\b\u0010\u001d\u001a\u0004\u0018\u00010\n2\u0006\u0010\u001e\u001a\u00020\n2\u0006\u0010\u001f\u001a\u00020\n2\b\b\u0002\u0010 \u001a\u00020!2\b\b\u0002\u0010\"\u001a\u00020!2\b\b\u0002\u0010#\u001a\u00020!2\f\u0010$\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010%\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\u0006\u0010&\u001a\u00020\'H\u0007\u001aD\u0010(\u001a\u00020\u00012\u0006\u0010)\u001a\u00020\n2\u0006\u0010*\u001a\u00020\n2\n\b\u0002\u0010+\u001a\u0004\u0018\u00010\n2\u0006\u0010,\u001a\u00020!2\u0006\u0010-\u001a\u00020!2\u0006\u0010.\u001a\u00020!2\u0006\u0010&\u001a\u00020\'H\u0007\u001aP\u0010/\u001a\u00020\u00012\u0006\u00100\u001a\u00020\u00142\u0006\u00101\u001a\u0002022\u0006\u00103\u001a\u0002042\u0006\u00105\u001a\u0002062\u0012\u00107\u001a\u000e\u0012\u0004\u0012\u00020\u0014\u0012\u0004\u0012\u00020\u0001082\u0012\u00109\u001a\u000e\u0012\u0004\u0012\u00020\u0014\u0012\u0004\u0012\u00020\u000108H\u0007\u001a\"\u0010:\u001a\u00020\u00012\u0006\u0010)\u001a\u00020\n2\u0006\u0010*\u001a\u00020\n2\b\u0010+\u001a\u0004\u0018\u00010\nH\u0007\u001a\u0082\u0001\u0010;\u001a\u00020\u00012\u0006\u0010<\u001a\u00020\n2\n\b\u0002\u0010=\u001a\u0004\u0018\u00010\n2\u0006\u0010>\u001a\u00020\n2\n\b\u0002\u0010?\u001a\u0004\u0018\u00010\n2\u0006\u0010@\u001a\u00020\n2\n\b\u0002\u0010A\u001a\u0004\u0018\u00010\n2\u0006\u0010B\u001a\u00020\n2\n\b\u0002\u0010C\u001a\u0004\u0018\u00010\n2\f\u0010D\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010E\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010F\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001a*\u0010G\u001a\b\u0012\u0004\u0012\u00020I0H2\f\u0010J\u001a\b\u0012\u0004\u0012\u00020K0H2\u000e\u0010L\u001a\n\u0012\u0004\u0012\u00020K\u0018\u00010H\u001a\u001d\u0010M\u001a\u00020\u00012\b\u0010\t\u001a\u0004\u0018\u00010\u00062\u0006\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0002\u0010N\u00a8\u0006O"}, d2 = {"CustomCreditSheetContent", "", "onDismiss", "Lkotlin/Function0;", "onSave", "Lkotlin/Function2;", "", "Lkotlin/ParameterName;", "name", "amount", "", "reason", "customerName", "amountState", "Landroidx/compose/runtime/MutableState;", "reasonState", "CustomCreditSheetContentBody", "CustomCreditSheetHeader", "GenericLineItem", "index", "", "description", "salePrice", "salePriceOld", "laborBudget", "laborBudgetOld", "profitOverhead", "profitOverheadOld", "materialBudget", "materialBudgetOld", "typeLabel", "updatedAt", "isNewItem", "", "isUpdatedItem", "isDeletedItem", "onEditClick", "onDeleteClick", "changeStatus", "Lcom/manaknight/app/ui/screens/ChangeStatus;", "InfoRow", "label", "newValue", "oldValue", "isNew", "isUpdated", "isDeleted", "LineItemsScreen", "projectID", "baasViewModel", "Lcom/manaknight/app/viewmodels/BaasViewModel;", "navController", "Landroidx/navigation/NavController;", "dialog", "Landroid/app/Dialog;", "onNavigateToPreview", "Lkotlin/Function1;", "deleteItem", "TotalRow", "TotalSummary", "totalSalePrice", "previousTotalSalePrice", "totalProfitOverhead", "previousTotalProfitOverhead", "totalMaterialBudget", "previousTotalMaterialBudget", "totalLaborBudget", "previousTotalLaborBudget", "onSaveAsDraftClick", "onSaveAndReviewClick", "onclick", "compareLineItems", "", "Lcom/manaknight/app/ui/screens/JobDetailsUIModel;", "newItems", "Lcom/manaknight/app/model/remote/profitPro/JobDetailsRespModel;", "oldItems", "onSaveCredit", "(Ljava/lang/Double;Ljava/lang/String;)V", "app_debug"})
public final class LineItemsScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void LineItemsScreen(int projectID, @org.jetbrains.annotations.NotNull()
    com.manaknight.app.viewmodels.BaasViewModel baasViewModel, @org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    android.app.Dialog dialog, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onNavigateToPreview, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> deleteItem) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.util.List<com.manaknight.app.ui.screens.JobDetailsUIModel> compareLineItems(@org.jetbrains.annotations.NotNull()
    java.util.List<com.manaknight.app.model.remote.profitPro.JobDetailsRespModel> newItems, @org.jetbrains.annotations.Nullable()
    java.util.List<com.manaknight.app.model.remote.profitPro.JobDetailsRespModel> oldItems) {
        return null;
    }
    
    @androidx.compose.runtime.Composable()
    public static final void GenericLineItem(int index, @org.jetbrains.annotations.NotNull()
    java.lang.String description, @org.jetbrains.annotations.NotNull()
    java.lang.String salePrice, @org.jetbrains.annotations.Nullable()
    java.lang.String salePriceOld, @org.jetbrains.annotations.NotNull()
    java.lang.String laborBudget, @org.jetbrains.annotations.Nullable()
    java.lang.String laborBudgetOld, @org.jetbrains.annotations.NotNull()
    java.lang.String profitOverhead, @org.jetbrains.annotations.Nullable()
    java.lang.String profitOverheadOld, @org.jetbrains.annotations.NotNull()
    java.lang.String materialBudget, @org.jetbrains.annotations.Nullable()
    java.lang.String materialBudgetOld, @org.jetbrains.annotations.NotNull()
    java.lang.String typeLabel, @org.jetbrains.annotations.NotNull()
    java.lang.String updatedAt, boolean isNewItem, boolean isUpdatedItem, boolean isDeletedItem, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onEditClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDeleteClick, @org.jetbrains.annotations.NotNull()
    com.manaknight.app.ui.screens.ChangeStatus changeStatus) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void InfoRow(@org.jetbrains.annotations.NotNull()
    java.lang.String label, @org.jetbrains.annotations.NotNull()
    java.lang.String newValue, @org.jetbrains.annotations.Nullable()
    java.lang.String oldValue, boolean isNew, boolean isUpdated, boolean isDeleted, @org.jetbrains.annotations.NotNull()
    com.manaknight.app.ui.screens.ChangeStatus changeStatus) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void TotalSummary(@org.jetbrains.annotations.NotNull()
    java.lang.String totalSalePrice, @org.jetbrains.annotations.Nullable()
    java.lang.String previousTotalSalePrice, @org.jetbrains.annotations.NotNull()
    java.lang.String totalProfitOverhead, @org.jetbrains.annotations.Nullable()
    java.lang.String previousTotalProfitOverhead, @org.jetbrains.annotations.NotNull()
    java.lang.String totalMaterialBudget, @org.jetbrains.annotations.Nullable()
    java.lang.String previousTotalMaterialBudget, @org.jetbrains.annotations.NotNull()
    java.lang.String totalLaborBudget, @org.jetbrains.annotations.Nullable()
    java.lang.String previousTotalLaborBudget, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onSaveAsDraftClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onSaveAndReviewClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onclick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void TotalRow(@org.jetbrains.annotations.NotNull()
    java.lang.String label, @org.jetbrains.annotations.NotNull()
    java.lang.String newValue, @org.jetbrains.annotations.Nullable()
    java.lang.String oldValue) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void CustomCreditSheetContent(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.Double, ? super java.lang.String, kotlin.Unit> onSave, @org.jetbrains.annotations.NotNull()
    java.lang.String customerName, @org.jetbrains.annotations.NotNull()
    androidx.compose.runtime.MutableState<java.lang.String> amountState, @org.jetbrains.annotations.NotNull()
    androidx.compose.runtime.MutableState<java.lang.String> reasonState) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void CustomCreditSheetHeader(@org.jetbrains.annotations.NotNull()
    java.lang.String customerName, @org.jetbrains.annotations.NotNull()
    androidx.compose.runtime.MutableState<java.lang.String> amountState, @org.jetbrains.annotations.NotNull()
    androidx.compose.runtime.MutableState<java.lang.String> reasonState, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.Double, ? super java.lang.String, kotlin.Unit> onSave) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void CustomCreditSheetContentBody(@org.jetbrains.annotations.NotNull()
    androidx.compose.runtime.MutableState<java.lang.String> amountState, @org.jetbrains.annotations.NotNull()
    androidx.compose.runtime.MutableState<java.lang.String> reasonState) {
    }
    
    public static final void onSaveCredit(@org.jetbrains.annotations.Nullable()
    java.lang.Double amount, @org.jetbrains.annotations.NotNull()
    java.lang.String reason) {
    }
}