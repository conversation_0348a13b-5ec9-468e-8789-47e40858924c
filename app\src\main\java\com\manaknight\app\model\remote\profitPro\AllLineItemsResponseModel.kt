
    package com.manaknight.app.model.remote.profitPro

    import android.os.Parcelable
    import com.manaknight.app.ui.screens.ChangeStatus
    import kotlinx.parcelize.Parcelize

    data class AllLineItemsResponseModel(
        val error: Boolean?,
        val job_details: ArrayList<JobDetailsRespModel>,
        val totals: TotalRespModel,
        val status: Int?,
        val client_details: ClientDetailRespModel,
        val draws: ArrayList<DrawsRespModel>,
        val create_at: String,
        val update_at: String
    )

    @Parcelize
    data class JobDetailsRespModel(
        val line_id: Int?,        val labour_hours: Int?,
        val labour_budget: Int?,
        val material_budget: Int?,
        val profit_overhead_amount: Double?,
        val sale_price: Double?,
        val estimated_by: String?,
        val description: String?,
        val materials: ArrayList<MaterialRespModel>,
        val update_at: String,
        val isNewItem: Boolean = false,
        val changeStatus: String? = null,
    ): Parcelable

    @Parcelize
    data class MaterialRespModel(
        val id: Int?,
        val cost: Int?,
        val quantity: Int?,
        val total: Int?,
        val name: String?
    ) : Parcelable

    data class TotalRespModel(
        val material_budget: Int?,
        val labour_budget: Int?,
        val total_profit_overhead: Double?,
        val sale_price: Double?
    )

    data class ClientDetailRespModel(
        val name: String?,
        val email: String?,
        val address: String?,
        val customer_id: Int?,
        val user_id: Int?
    )

    data class DrawsRespModel(
        val amount_paid: Int?,
        val id: Int?,
        val check_no: String?,
        val amount: String?,
        val percentage: String?,
        val description: String?,
        val project_id: Int?,
        val payment_type: String?,
        val status: String?,
        val remainingBalance: String?,
        val remainingPercentage: String?,
    )

    data class DrawInfoRespModel(
        val error: Boolean?,
        val message: String?,
        val model: DrawInfoModelRespModel?,
    )

    data class DrawInfoModelRespModel(

        val draws: ArrayList<DrawsRespModel>,
        val unaccounted_amount: String?,
        val sale_price: String?,
    )
