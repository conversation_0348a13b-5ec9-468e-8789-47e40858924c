<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_home" modulePackage="Manaknight" filePath="app\src\main\res\layout\fragment_home.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_home_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="643" endOffset="51"/></Target><Target tag="binding_1" view="LinearLayout"><Expressions/><location startLine="6" startOffset="4" endLine="641" endOffset="18"/></Target><Target id="@+id/headerInclude" tag="binding_1" include="header"><Expressions/><location startLine="13" startOffset="8" endLine="15" endOffset="37"/></Target><Target id="@+id/txtTotalProfitOverhead" view="TextView"><Expressions/><location startLine="76" startOffset="36" endLine="87" endOffset="65"/></Target><Target id="@+id/txtCompanyHealth" view="TextView"><Expressions/><location startLine="133" startOffset="36" endLine="144" endOffset="66"/></Target><Target id="@+id/btnViewDetails" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="157" startOffset="16" endLine="171" endOffset="49"/></Target><Target id="@+id/txtTotalContracts" view="TextView"><Expressions/><location startLine="200" startOffset="28" endLine="209" endOffset="73"/></Target><Target id="@+id/txtTotalAR" view="TextView"><Expressions/><location startLine="231" startOffset="28" endLine="246" endOffset="67"/></Target><Target id="@+id/materialBalanceScroll" view="HorizontalScrollView"><Expressions/><location startLine="279" startOffset="28" endLine="310" endOffset="46"/></Target><Target id="@+id/txtMaterialBalance" view="TextView"><Expressions/><location startLine="293" startOffset="32" endLine="308" endOffset="71"/></Target><Target id="@+id/txtMaterialSpent" view="TextView"><Expressions/><location startLine="332" startOffset="28" endLine="347" endOffset="67"/></Target><Target id="@+id/txtLaborBalance" view="TextView"><Expressions/><location startLine="378" startOffset="28" endLine="393" endOffset="67"/></Target><Target id="@+id/txtLaborSpent" view="TextView"><Expressions/><location startLine="415" startOffset="28" endLine="430" endOffset="67"/></Target><Target id="@+id/firstRow" view="LinearLayout"><Expressions/><location startLine="456" startOffset="20" endLine="528" endOffset="34"/></Target><Target id="@+id/draftsCard" view="LinearLayout"><Expressions/><location startLine="463" startOffset="24" endLine="493" endOffset="38"/></Target><Target id="@+id/txtDraftsCount" view="TextView"><Expressions/><location startLine="472" startOffset="28" endLine="481" endOffset="61"/></Target><Target id="@+id/outstandingCard" view="LinearLayout"><Expressions/><location startLine="497" startOffset="24" endLine="527" endOffset="38"/></Target><Target id="@+id/txtOutstandingCount" view="TextView"><Expressions/><location startLine="506" startOffset="28" endLine="515" endOffset="73"/></Target><Target id="@+id/activeCard" view="LinearLayout"><Expressions/><location startLine="541" startOffset="24" endLine="571" endOffset="38"/></Target><Target id="@+id/txtActiveCount" view="TextView"><Expressions/><location startLine="550" startOffset="28" endLine="559" endOffset="73"/></Target><Target id="@+id/completedCard" view="LinearLayout"><Expressions/><location startLine="575" startOffset="24" endLine="605" endOffset="38"/></Target><Target id="@+id/txtCompletedCount" view="TextView"><Expressions/><location startLine="584" startOffset="28" endLine="593" endOffset="73"/></Target><Target id="@+id/addNewEstimation" view="ImageView"><Expressions/><location startLine="609" startOffset="20" endLine="617" endOffset="62"/></Target></Targets></Layout>