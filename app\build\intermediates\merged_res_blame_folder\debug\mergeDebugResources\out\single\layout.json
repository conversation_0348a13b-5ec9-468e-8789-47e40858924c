[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\activity_main.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\fragment_subscription.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\fragment_subscription.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\item_app_alert.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\item_app_alert.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\fragment_labortrackingview.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\fragment_labortrackingview.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\fragment_accountview.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\fragment_accountview.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\fragment_create_customer.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\fragment_create_customer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\fragment_draws.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\fragment_draws.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\fragment_room_list.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\fragment_room_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\user_data.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\user_data.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\bottom_sheet_month_filter.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\bottom_sheet_month_filter.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\item_dashboard_project.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\item_dashboard_project.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\item_broadcast_video.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\item_broadcast_video.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\fragment_dashboardview.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\fragment_dashboardview.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\bottom_sheet_status_filter.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\bottom_sheet_status_filter.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\receive_image_message_item.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\receive_image_message_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\fragment_alerts.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\fragment_alerts.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\item_line.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\item_line.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\receive_video_message_item.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\receive_video_message_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\item_draw.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\item_draw.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\dialog_alert_view.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\dialog_alert_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\dialog_add_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\dialog_add_material.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\bottom_add_draw.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\bottom_add_draw.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\fragment_workerview.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\fragment_workerview.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\bottom_add_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\bottom_add_material.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\bottom_add_employee.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\bottom_add_employee.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\fragment_create_estimation.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\fragment_create_estimation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\fragment_costview.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\fragment_costview.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\dialog_forgetpassword.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\dialog_forgetpassword.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\dialog_add_square.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\dialog_add_square.xml"}, {"merged": "Manaknight.app-mergeDebugResources-105:/layout/fragment_dashboardview.xml", "source": "Manaknight.app-main-108:/layout/fragment_dashboardview.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\simple_chat_view_widget.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\simple_chat_view_widget.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\room_data.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\room_data.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\fragment_completesetup.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\fragment_completesetup.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\fragment_home.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\fragment_home.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\header.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\header.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\dialog_add_lineal.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\dialog_add_lineal.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\fragment_forget_password.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\fragment_forget_password.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\send_video_message_item.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\send_video_message_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\fragment_linear_line_item.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\fragment_linear_line_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\fragment_line_items.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\fragment_line_items.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\fragment_projectview.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\fragment_projectview.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\fragment_squresetup.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\fragment_squresetup.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\fragment_splash.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\fragment_splash.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\item_line_total.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\item_line_total.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\fragment_companysetup.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\fragment_companysetup.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\fragment_profileview.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\fragment_profileview.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\fragment_add_line_items.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\fragment_add_line_items.xml"}, {"merged": "Manaknight.app-mergeDebugResources-105:/layout/fragment_home.xml", "source": "Manaknight.app-main-108:/layout/fragment_home.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\fragment_trackingview.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\fragment_trackingview.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\send_text_message_item.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\send_text_message_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\bottom_select_customer.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\bottom_select_customer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\receive_text_message_item.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\receive_text_message_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\item_line_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\item_line_material.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\bottom_update_password.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\bottom_update_password.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\fragment_profile.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\fragment_profile.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\fragment_sign_up.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\fragment_sign_up.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\item_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\item_material.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\progress_dialog.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\progress_dialog.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\bottom_sheet_multi_select_status_filter.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\bottom_sheet_multi_select_status_filter.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\send_image_message_item.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\send_image_message_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\fragment_linealsetup.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\fragment_linealsetup.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\fragment_reset_password.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\fragment_reset_password.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\dialog_resetpassword.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\dialog_resetpassword.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\fragment_login.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\fragment_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\item_employee.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\item_employee.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\item_filter_option.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\item_filter_option.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\bottom_add_lineal.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\bottom_add_lineal.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\bottom_update_profile.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\bottom_update_profile.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\item_multi_select_filter_option.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\item_multi_select_filter_option.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\fragment_friend_list.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\fragment_friend_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\fragment_materialsetup.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\fragment_materialsetup.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\fragment_profile_edit.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\fragment_profile_edit.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\item_lineal.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\item_lineal.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\item_customer.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\item_customer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\item_line_lineal.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\item_line_lineal.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\fragment_material_line_item.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\fragment_material_line_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-mergeDebugResources-105:\\layout\\bottom_edit_profile.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-108:\\layout\\bottom_edit_profile.xml"}]